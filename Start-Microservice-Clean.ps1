# Clean Microservice Startup Script
Write-Host "=== SPMJ Email Microservice Clean Startup ===" -ForegroundColor Green
Write-Host ""

# Step 1: Kill any existing processes
Write-Host "Step 1: Cleaning up existing processes..." -ForegroundColor Yellow
try {
    # Kill any dotnet processes
    Get-Process -Name "dotnet" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Stopped existing dotnet processes" -ForegroundColor Green
} catch {
    Write-Host "⚠️  No existing dotnet processes to stop" -ForegroundColor Yellow
}

try {
    # Kill any SPMJ.EmailService processes
    Get-Process -Name "SPMJ.EmailService" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Stopped existing SPMJ.EmailService processes" -ForegroundColor Green
} catch {
    Write-Host "⚠️  No existing SPMJ.EmailService processes to stop" -ForegroundColor Yellow
}

# Step 2: Check and free port 5000
Write-Host ""
Write-Host "Step 2: Checking port 5000..." -ForegroundColor Yellow
$portInUse = netstat -ano | Select-String ":5000.*LISTENING"
if ($portInUse) {
    Write-Host "Port 5000 is in use, attempting to free it..." -ForegroundColor Yellow
    $portInUse | ForEach-Object {
        $line = $_.Line
        $pid = ($line -split '\s+')[-1]
        if ($pid -match '^\d+$') {
            try {
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
                Write-Host "✅ Killed process $pid using port 5000" -ForegroundColor Green
            } catch {
                Write-Host "⚠️  Could not kill process $pid" -ForegroundColor Yellow
            }
        }
    }
    Start-Sleep -Seconds 2
} else {
    Write-Host "✅ Port 5000 is free" -ForegroundColor Green
}

# Step 3: Verify microservice directory and build
Write-Host ""
Write-Host "Step 3: Preparing microservice..." -ForegroundColor Yellow
$microserviceDir = "SPMJ.EmailService"
if (Test-Path $microserviceDir) {
    Write-Host "✅ Microservice directory found" -ForegroundColor Green
    
    # Build the project
    Write-Host "Building microservice..." -ForegroundColor Gray
    Set-Location $microserviceDir
    $buildResult = & dotnet build 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Build failed:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    Set-Location ..
} else {
    Write-Host "❌ Microservice directory not found: $microserviceDir" -ForegroundColor Red
    exit 1
}

# Step 4: Start microservice
Write-Host ""
Write-Host "Step 4: Starting microservice..." -ForegroundColor Yellow
try {
    Set-Location $microserviceDir
    
    # Start the microservice in background
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run", "--urls", "http://localhost:5000" -PassThru -WindowStyle Hidden
    
    Write-Host "✅ Microservice started with PID: $($process.Id)" -ForegroundColor Green
    Write-Host "Waiting for initialization..." -ForegroundColor Gray
    
    Set-Location ..
    
    # Wait for service to be ready
    $maxAttempts = 15
    $attempt = 0
    $serviceReady = $false
    
    while ($attempt -lt $maxAttempts -and -not $serviceReady) {
        Start-Sleep -Seconds 2
        $attempt++
        
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:5000/health" -TimeoutSec 3
            if ($response.status -eq "healthy") {
                $serviceReady = $true
                Write-Host "✅ Microservice is healthy and responding" -ForegroundColor Green
            }
        } catch {
            Write-Host "⏳ Attempt $attempt/$maxAttempts - waiting for microservice..." -ForegroundColor Gray
        }
    }
    
    if (-not $serviceReady) {
        Write-Host "❌ Microservice failed to start properly" -ForegroundColor Red
        Write-Host "Checking process status..." -ForegroundColor Yellow
        if ($process.HasExited) {
            Write-Host "Process has exited with code: $($process.ExitCode)" -ForegroundColor Red
        } else {
            Write-Host "Process is still running but not responding" -ForegroundColor Yellow
        }
        exit 1
    }
    
} catch {
    Write-Host "❌ Failed to start microservice: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Step 5: Test endpoints
Write-Host ""
Write-Host "Step 5: Testing microservice endpoints..." -ForegroundColor Yellow

$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
}

# Test health endpoint
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Health endpoint: $($healthResponse.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ Health endpoint failed" -ForegroundColor Red
}

# Test password reset endpoint
try {
    $testRequest = @{
        UserId = "test"
        Email = "<EMAIL>"
        UserName = "Test User"
        TempPassword = "TestPass123!"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/password/reset/send" -Method POST -Body $testRequest -Headers $headers -TimeoutSec 5
    if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 400) {
        Write-Host "✅ Password reset endpoint: Available" -ForegroundColor Green
    }
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ Password reset endpoint: Available (400 expected for test data)" -ForegroundColor Green
    } else {
        Write-Host "❌ Password reset endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 6: Final status
Write-Host ""
Write-Host "=== Microservice Startup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Status: ✅ RUNNING" -ForegroundColor Cyan
Write-Host "📍 URL: http://localhost:5000" -ForegroundColor Gray
Write-Host "🔍 Health: http://localhost:5000/health" -ForegroundColor Gray
Write-Host "📧 Password Reset: http://localhost:5000/api/password/reset/send" -ForegroundColor Gray
Write-Host ""
Write-Host "The password recovery function should now work!" -ForegroundColor Green
Write-Host ""
Write-Host "To test:" -ForegroundColor Yellow
Write-Host "1. Go to p0_Login.aspx" -ForegroundColor Gray
Write-Host "2. Click 'Lupa Kata Laluan?' link" -ForegroundColor Gray
Write-Host "3. Enter User ID: 820228115693" -ForegroundColor Gray
Write-Host "4. Click 'Hantar' button" -ForegroundColor Gray
Write-Host "5. Check for success message or email" -ForegroundColor Gray
Write-Host ""
Write-Host "To stop the microservice, close this window or press Ctrl+C" -ForegroundColor Yellow
