# Verify PN_Pwd.aspx Direct Database Connection (No Fallbacks)
Write-Host "=== PN_Pwd.aspx Direct Database Connection Verification ===" -ForegroundColor Green
Write-Host ""

# Check Web.config connection string
Write-Host "1. Web.config Connection String Check:" -ForegroundColor Yellow
$webConfigPath = "SPMJ -PDSA\SPMJ\Web.config"
if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    
    if ($webConfig -match 'DefaultConnection.*SQLOLEDB\.1.*localhost.*SPMJ_PDSA.*sa.*OhyHf1982') {
        Write-Host "   ✅ DefaultConnection matches SPMJ_Mod pattern" -ForegroundColor Green
    } else {
        Write-Host "   ❌ DefaultConnection does not match SPMJ_Mod pattern" -ForegroundColor Red
    }
    
    if ($webConfig -match 'LoginConnection.*ro.*OhyHf1982') {
        Write-Host "   ✅ LoginConnection configured for read-only access" -ForegroundColor Green
    } else {
        Write-Host "   ❌ LoginConnection missing or incorrect" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ Web.config not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. PN_Pwd.aspx.vb Direct Connection Check:" -ForegroundColor Yellow
$vbPath = "SPMJ -PDSA\SPMJ\PN_Pwd.aspx.vb"
if (Test-Path $vbPath) {
    $vbContent = Get-Content $vbPath -Raw
    
    # Check for direct SPMJ_Mod.ServerId usage
    if ($vbContent -match 'Return SPMJ_Mod\.ServerId') {
        Write-Host "   ✅ Using direct SPMJ_Mod.ServerId connection" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Not using direct SPMJ_Mod.ServerId connection" -ForegroundColor Red
    }
    
    # Check that fallback logic is removed
    if ($vbContent -notmatch 'Try.*web\.config.*Catch.*fallback') {
        Write-Host "   ✅ Fallback logic removed" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Fallback logic still present" -ForegroundColor Red
    }
    
    # Check for database connection test
    if ($vbContent -match 'TestDatabaseConnection') {
        Write-Host "   ✅ Database connection test implemented" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Database connection test missing" -ForegroundColor Red
    }
    
    # Check for simplified database update
    if ($vbContent -match 'UPDATE pn_pengguna SET pwd = \?, salt = \? WHERE id_pg = \?') {
        Write-Host "   ✅ Direct database update (no fallback)" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Database update still has fallback logic" -ForegroundColor Red
    }
    
    # Check that enhanced update fallback is removed
    if ($vbContent -notmatch 'Try.*enhanced update.*Catch.*basic update') {
        Write-Host "   ✅ Database update fallback removed" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Database update fallback still present" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ PN_Pwd.aspx.vb not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. SPMJ_Mod.vb Configuration Check:" -ForegroundColor Yellow
$spmjModPath = "SPMJ -PDSA\SPMJ\SPMJ_Mod.vb"
if (Test-Path $spmjModPath) {
    $spmjModContent = Get-Content $spmjModPath -Raw
    
    if ($spmjModContent -match 'Public ServerId As String = "Provider=SQLOLEDB\.1.*localhost.*SPMJ_PDSA.*sa.*OhyHf1982"') {
        Write-Host "   ✅ SPMJ_Mod.ServerId properly configured" -ForegroundColor Green
    } else {
        Write-Host "   ❌ SPMJ_Mod.ServerId not properly configured" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ SPMJ_Mod.vb not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. App Settings Check:" -ForegroundColor Yellow
if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    
    if ($webConfig -match 'IP_App.*localhost') {
        Write-Host "   ✅ IP_App set to localhost" -ForegroundColor Green
    } else {
        Write-Host "   ❌ IP_App not set to localhost" -ForegroundColor Red
    }
    
    if ($webConfig -match 'dB.*SPMJ_PDSA') {
        Write-Host "   ✅ Database name set to SPMJ_PDSA" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Database name not set to SPMJ_PDSA" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Cyan
Write-Host "✅ Removed all database connection fallbacks" -ForegroundColor White
Write-Host "✅ Using direct SPMJ_Mod.ServerId connection" -ForegroundColor White
Write-Host "✅ Updated Web.config to match SPMJ_Mod pattern" -ForegroundColor White
Write-Host "✅ Added database connection test at page load" -ForegroundColor White
Write-Host "✅ Simplified database update logic" -ForegroundColor White
Write-Host "✅ Ensured consistent connection throughout" -ForegroundColor White

Write-Host ""
Write-Host "🎯 PN_Pwd.aspx now has DIRECT database connection without fallbacks!" -ForegroundColor Green
Write-Host ""
Write-Host "Connection Details:" -ForegroundColor Yellow
Write-Host "• Provider: SQLOLEDB.1" -ForegroundColor White
Write-Host "• Server: localhost" -ForegroundColor White
Write-Host "• Database: SPMJ_PDSA" -ForegroundColor White
Write-Host "• User: sa" -ForegroundColor White
Write-Host "• Password: OhyHf1982" -ForegroundColor White
