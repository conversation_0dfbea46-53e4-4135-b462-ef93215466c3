﻿Imports System.Data.OleDb
Imports System.Security.Cryptography
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Data
Imports System.Collections.Generic
Imports System.Web.UI.WebControls
Imports System.Web.Services

Partial Public Class WebForm13
    Inherits System.Web.UI.Page

    ' Email service client for microservice integration
    Private emailClient As EmailServiceClient

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        
        ' Check authentication
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString() = "" Then
            Session.Abandon()
            Response.Redirect("p0_Login.aspx")
            Return
        End If
          ' Test database connection first
        Try
            TestDatabaseConnection()
            System.Diagnostics.Debug.WriteLine("✅ Database connection test successful")
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Database connection test failed: " & ex.Message)
            ShowMessage("Database connection error. Please contact system administrator.", "error")
            Return
        End Try

        ' Initialize email service client
        Try
            ' Get email service URL from web.config
            Dim emailServiceUrl As String = System.Configuration.ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(emailServiceUrl) Then
                emailServiceUrl = "http://localhost:5000" ' Default fallback
            End If

            emailClient = New EmailServiceClient(emailServiceUrl)
            System.Diagnostics.Debug.WriteLine("✅ Email service client initialized successfully with URL: " & emailServiceUrl)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("⚠️ Email service client initialization failed: " & ex.Message)
            ' Continue without email service - will disable notifications
        End Try
        
        ' Configure form security
        ConfigureFormSecurity()
        
        ' Set focus to current password field
        Tx_CurrentPassword.Focus()
        
        ' Log page access for security auditing
        LogSecurityEvent("PASSWORD_CHANGE_PAGE_ACCESS", Session("Id_PG").ToString())
    End Sub

    Private Sub ConfigureFormSecurity()
        ' Enhanced form security configuration
        Tx_CurrentPassword.Attributes.Add("autocomplete", "current-password")
        Tx_NewPassword.Attributes.Add("autocomplete", "new-password")
        Tx_ConfirmPassword.Attributes.Add("autocomplete", "new-password")
        
        ' Add security headers
        Response.Headers.Add("X-Content-Type-Options", "nosniff")
        Response.Headers.Add("X-Frame-Options", "DENY")
        Response.Headers.Add("X-XSS-Protection", "1; mode=block")
        
        ' Increase MaxLength for industry standard passwords
        Tx_CurrentPassword.MaxLength = 100
        Tx_NewPassword.MaxLength = 100
        Tx_ConfirmPassword.MaxLength = 100
    End Sub

    Protected Sub btn_ChangePassword_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btn_ChangePassword.Click
        Try
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Password change initiated for user: " & Session("Id_PG").ToString())
            
            ' Clear any previous messages
            HideMessage()
            
            ' Server-side validation
            If Not ValidatePasswordChangeRequest() Then
                Return
            End If
            
            ' Verify current password with enhanced security
            If Not VerifyCurrentPasswordSecure() Then
                ShowMessage("Current password is incorrect. Please try again.", "error")
                Tx_CurrentPassword.Focus()
                LogSecurityEvent("PASSWORD_CHANGE_FAILED_AUTH", Session("Id_PG").ToString())
                Return
            End If
            
            ' Validate new password with industry standards
            Dim passwordValidation As PasswordValidationResult = ValidatePasswordStrengthIndustryStandard(Tx_NewPassword.Text)
            If Not passwordValidation.IsValid Then
                ShowMessage("New password does not meet requirements: " & String.Join(", ", passwordValidation.Errors.ToArray()), "error")
                Tx_NewPassword.Focus()
                Return
            End If
            
            ' Check if new password is different from current (enhanced check)
            If Not ValidatePasswordDifference() Then
                ShowMessage("New password must be different from current password.", "error")
                Tx_NewPassword.Focus()
                Return
            End If
            
            ' Update password with industry standard encryption
            If UpdatePasswordIndustryStandard() Then
                ' Send email notification via microservice (if enabled and available)
                Dim emailSent As Boolean = False
                If chkEmailNotification.Checked Then
                    emailSent = SendPasswordChangeNotification()
                End If
                
                ' Log successful password change
                LogSecurityEvent("PASSWORD_CHANGE_SUCCESS", Session("Id_PG").ToString())
                
                ' Clear form for security
                ClearPasswordFields()
                
                ' Show success message with microservice status
                Dim successMessage As String = "Password has been successfully updated with industry-standard SHA256+Salt encryption!"
                If chkEmailNotification.Checked Then
                    If emailSent Then
                        successMessage &= " Confirmation email has been sent to your registered email address."
                    Else
                        successMessage &= " Note: Email notification could not be sent due to service unavailability."
                    End If
                End If
                successMessage &= " Please log out and log in again for optimal security."
                
                ShowMessage(successMessage, "success")
                
                ' Optional: Auto-logout for enhanced security
                ' Session.Abandon()
                ' Response.Redirect("p0_Login.aspx")
            Else
                LogSecurityEvent("PASSWORD_CHANGE_FAILED_UPDATE", Session("Id_PG").ToString())
                ShowMessage("Failed to update password. Please try again or contact system administrator.", "error")
            End If
            
        Catch ex As Exception
            LogSecurityEvent("PASSWORD_CHANGE_ERROR", Session("Id_PG").ToString(), ex.Message)
            ShowMessage("System error occurred. Please try again or contact system administrator.", "error")
            System.Diagnostics.Debug.WriteLine("Password change error: " & ex.Message)
        End Try
    End Sub

    Protected Sub btn_Cancel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btn_Cancel.Click
        ' Clear all fields and redirect
        ClearPasswordFields()
        HideMessage()
        Response.Redirect("Main.aspx")
    End Sub

#Region "Industry Standard Validation Methods"

    Private Function ValidatePasswordChangeRequest() As Boolean
        ' Enhanced validation with detailed error messages
        
        ' Check current password
        If String.IsNullOrEmpty(Tx_CurrentPassword.Text) Then
            ShowMessage("Please enter your current password.", "error")
            Tx_CurrentPassword.Focus()
            Return False
        End If
        
        ' Check new password
        If String.IsNullOrEmpty(Tx_NewPassword.Text) Then
            ShowMessage("Please enter a new password.", "error")
            Tx_NewPassword.Focus()
            Return False
        End If
        
        ' Check password confirmation
        If String.IsNullOrEmpty(Tx_ConfirmPassword.Text) Then
            ShowMessage("Please confirm your new password.", "error")
            Tx_ConfirmPassword.Focus()
            Return False
        End If
        
        ' Check password confirmation match
        If Tx_NewPassword.Text <> Tx_ConfirmPassword.Text Then
            ShowMessage("New password and confirmation do not match.", "error")
            Tx_ConfirmPassword.Focus()
            Return False
        End If
        
        Return True
    End Function

    Private Function ValidatePasswordStrengthIndustryStandard(password As String) As PasswordValidationResult
        Dim result As New PasswordValidationResult()
        result.IsValid = True
        result.Errors = New List(Of String)
        
        ' Industry standard requirements (6 criteria)
        
        ' 1. Minimum length (8 characters)
        If password.Length < 8 Then
            result.Errors.Add("Password must be at least 8 characters long")
            result.IsValid = False
        End If
        
        ' 2. Maximum length check (prevent extremely long passwords)
        If password.Length > 100 Then
            result.Errors.Add("Password must not exceed 100 characters")
            result.IsValid = False
        End If
        
        ' 3. Uppercase letter requirement
        If Not Regex.IsMatch(password, "[A-Z]") Then
            result.Errors.Add("Password must contain at least one uppercase letter (A-Z)")
            result.IsValid = False
        End If
        
        ' 4. Lowercase letter requirement
        If Not Regex.IsMatch(password, "[a-z]") Then
            result.Errors.Add("Password must contain at least one lowercase letter (a-z)")
            result.IsValid = False
        End If
        
        ' 5. Number requirement
        If Not Regex.IsMatch(password, "[0-9]") Then
            result.Errors.Add("Password must contain at least one number (0-9)")
            result.IsValid = False
        End If
        
        ' 6. Special character requirement (industry standard)
        If Not Regex.IsMatch(password, "[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]") Then
            result.Errors.Add("Password must contain at least one special character (!@#$%^&*)")
            result.IsValid = False
        End If
        
        ' Additional security checks
        
        ' Check for common weak passwords
        Dim commonPasswords() As String = {"password", "123456", "qwerty", "admin", "letmein", "welcome", "monkey", "1234567890"}
        If Array.Exists(commonPasswords, Function(x) x.ToLower() = password.ToLower()) Then
            result.Errors.Add("Password is too common and easily guessable")
            result.IsValid = False
        End If
        
        ' Check for sequential characters
        If HasSequentialCharacters(password) Then
            result.Errors.Add("Password should not contain sequential characters")
            result.IsValid = False
        End If
        
        ' Check for repeated characters (more than 3 consecutive)
        If HasRepeatedCharacters(password, 3) Then
            result.Errors.Add("Password should not contain more than 3 repeated characters")
            result.IsValid = False
        End If
        
        Return result
    End Function

    Private Function ValidatePasswordDifference() As Boolean
        ' Enhanced check to ensure new password is different from current
        ' This includes case-insensitive comparison and minor variation detection
        
        Dim currentPassword As String = Tx_CurrentPassword.Text
        Dim newPassword As String = Tx_NewPassword.Text
        
        ' Direct comparison
        If currentPassword = newPassword Then
            Return False
        End If
        
        ' Case-insensitive comparison
        If currentPassword.ToLower() = newPassword.ToLower() Then
            Return False
        End If
        
        ' Check for minor variations (adding/removing single character)
        If Math.Abs(currentPassword.Length - newPassword.Length) <= 1 Then
            Dim similarity As Double = CalculateStringSimilarity(currentPassword, newPassword)
            If similarity > 0.8 Then  ' 80% similarity threshold
                Return False
            End If
        End If
        
        Return True
    End Function

    Private Function HasSequentialCharacters(password As String) As Boolean
        ' Check for sequences like "123", "abc", "qwe"
        For i As Integer = 0 To password.Length - 3
            Dim char1 As Char = password(i)
            Dim char2 As Char = password(i + 1)
            Dim char3 As Char = password(i + 2)
            
            If (Asc(char2) = Asc(char1) + 1) AndAlso (Asc(char3) = Asc(char2) + 1) Then
                Return True
            End If
        Next
        Return False
    End Function

    Private Function HasRepeatedCharacters(password As String, maxRepeats As Integer) As Boolean
        ' Check for repeated characters
        Dim count As Integer = 1
        For i As Integer = 1 To password.Length - 1
            If password(i) = password(i - 1) Then
                count += 1
                If count > maxRepeats Then
                    Return True
                End If
            Else
                count = 1
            End If
        Next
        Return False
    End Function

    Private Function CalculateStringSimilarity(str1 As String, str2 As String) As Double
        ' Simple string similarity calculation (Levenshtein distance based)
        Dim maxLen As Integer = Math.Max(str1.Length, str2.Length)
        If maxLen = 0 Then Return 1.0
        
        Dim distance As Integer = LevenshteinDistance(str1, str2)
        Return 1.0 - (distance / maxLen)
    End Function

    Private Function LevenshteinDistance(str1 As String, str2 As String) As Integer
        ' Calculate Levenshtein distance between two strings
        Dim len1 As Integer = str1.Length
        Dim len2 As Integer = str2.Length
        Dim matrix(len1, len2) As Integer
        
        For i As Integer = 0 To len1
            matrix(i, 0) = i
        Next
        
        For j As Integer = 0 To len2
            matrix(0, j) = j
        Next
        
        For i As Integer = 1 To len1
            For j As Integer = 1 To len2
                Dim cost As Integer = If(str1(i - 1) = str2(j - 1), 0, 1)
                matrix(i, j) = Math.Min(Math.Min(matrix(i - 1, j) + 1, matrix(i, j - 1) + 1), matrix(i - 1, j - 1) + cost)
            Next
        Next
        
        Return matrix(len1, len2)
    End Function

#End Region

#Region "Industry Standard Password Management"

    Private Function VerifyCurrentPasswordSecure() As Boolean
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()

            Dim userId As String = Session("Id_PG").ToString()
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Verifying current password for user: " & userId)

            ' First try the enhanced method
            Dim result As Boolean = VerifyPasswordEnhanced(connection, userId)
            If result Then
                System.Diagnostics.Debug.WriteLine("✅ Enhanced verification succeeded")
                Return True
            End If

            ' Fallback to basic method
            System.Diagnostics.Debug.WriteLine("⚠️ Enhanced verification failed, trying basic method...")
            Return VerifyPasswordBasic(connection, userId)
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Current password verification error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

    Private Function VerifyPasswordEnhanced(connection As OleDbConnection, userId As String) As Boolean
        Try
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT pwd, salt, pwd_encrypted FROM pn_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)

                System.Diagnostics.Debug.WriteLine("ENHANCED VERIFY: Executing query for user: " & userId)

                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        System.Diagnostics.Debug.WriteLine("ENHANCED VERIFY: User record found")

                        Dim storedPassword As String = GetSafeStringValue(reader, "pwd")
                        Dim salt As String = GetSafeStringValue(reader, "salt")
                        Dim isEncrypted As Boolean = GetSafeBoolValue(reader, "pwd_encrypted")

                        System.Diagnostics.Debug.WriteLine("ENHANCED VERIFY: pwd='" & storedPassword & "', salt='" & salt & "', encrypted=" & isEncrypted)

                        ' Try all verification methods
                        Return TryAllVerificationMethods(Tx_CurrentPassword.Text, storedPassword, salt, isEncrypted)
                    Else
                        System.Diagnostics.Debug.WriteLine("ENHANCED VERIFY: No user record found")
                        Return False
                    End If
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("ENHANCED VERIFY: Error - " & ex.Message)
            Return False
        End Try
    End Function

    Private Function VerifyPasswordBasic(connection As OleDbConnection, userId As String) As Boolean
        Try
            ' Try with minimal query - just pwd column
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT pwd FROM pn_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)

                System.Diagnostics.Debug.WriteLine("BASIC VERIFY: Executing minimal query for user: " & userId)

                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        Dim storedPassword As String = If(reader("pwd") Is DBNull.Value, "", reader("pwd").ToString())
                        System.Diagnostics.Debug.WriteLine("BASIC VERIFY: Found password: '" & storedPassword & "'")

                        ' Simple comparison methods
                        If storedPassword.Equals(Tx_CurrentPassword.Text) Then
                            System.Diagnostics.Debug.WriteLine("BASIC VERIFY: ✅ Exact match")
                            Return True
                        End If

                        If storedPassword.Trim().Equals(Tx_CurrentPassword.Text.Trim()) Then
                            System.Diagnostics.Debug.WriteLine("BASIC VERIFY: ✅ Trimmed match")
                            Return True
                        End If

                        System.Diagnostics.Debug.WriteLine("BASIC VERIFY: ❌ No match found")
                        Return False
                    Else
                        System.Diagnostics.Debug.WriteLine("BASIC VERIFY: No user record found")
                        Return False
                    End If
                End Using
            End Using
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("BASIC VERIFY: Error - " & ex.Message)
            Return False
        End Try
    End Function

    Private Function TryAllVerificationMethods(inputPassword As String, storedPassword As String, salt As String, isEncrypted As Boolean) As Boolean
        System.Diagnostics.Debug.WriteLine("TRYING ALL METHODS: input='" & inputPassword & "', stored='" & storedPassword & "', salt='" & salt & "', encrypted=" & isEncrypted)

        ' Method 1: If encrypted and has salt
        If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
            System.Diagnostics.Debug.WriteLine("METHOD 1: Trying encrypted verification...")

            ' Standard verification
            Try
                If PasswordHelper.VerifyPassword(inputPassword, storedPassword, salt) Then
                    System.Diagnostics.Debug.WriteLine("METHOD 1a: ✅ Standard verification succeeded")
                    Return True
                End If
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("METHOD 1a: Error - " & ex.Message)
            End Try

            ' Workaround verification
            Try
                If PasswordHelper.VerifyPasswordWorkaround(inputPassword, storedPassword, salt) Then
                    System.Diagnostics.Debug.WriteLine("METHOD 1b: ✅ Workaround verification succeeded")
                    Return True
                End If
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine("METHOD 1b: Error - " & ex.Message)
            End Try
        End If

        ' Method 2: Plain text comparisons
        System.Diagnostics.Debug.WriteLine("METHOD 2: Trying plain text verification...")

        If storedPassword.Equals(inputPassword) Then
            System.Diagnostics.Debug.WriteLine("METHOD 2a: ✅ Exact plain text match")
            Return True
        End If

        If storedPassword.Trim().Equals(inputPassword.Trim()) Then
            System.Diagnostics.Debug.WriteLine("METHOD 2b: ✅ Trimmed plain text match")
            Return True
        End If

        If storedPassword.ToLower().Equals(inputPassword.ToLower()) Then
            System.Diagnostics.Debug.WriteLine("METHOD 2c: ✅ Case-insensitive match")
            Return True
        End If

        System.Diagnostics.Debug.WriteLine("ALL METHODS: ❌ All verification methods failed")
        Return False
    End Function

    Private Function UpdatePasswordIndustryStandard() As Boolean
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Dim userId As String = Session("Id_PG").ToString()
            System.Diagnostics.Debug.WriteLine("INDUSTRY STANDARD: Updating password for user: " & userId)
            
            ' Create encrypted password with industry standard SHA256+Salt
            Dim passwordEntry() As String = PasswordHelper.CreatePasswordEntry(Tx_NewPassword.Text)
            Dim hashedPassword As String = passwordEntry(0)  ' SHA256 hash
            Dim salt As String = passwordEntry(1)            ' Cryptographic salt
            
            System.Diagnostics.Debug.WriteLine("  Generated hash length: " & hashedPassword.Length)
            System.Diagnostics.Debug.WriteLine("  Generated salt length: " & salt.Length)
            
            Using command As New OleDbCommand()
                command.Connection = connection

                ' Direct database update using standard pn_pengguna table structure
                command.CommandText = "UPDATE pn_pengguna SET pwd = ?, salt = ? WHERE id_pg = ?"
                command.Parameters.AddWithValue("@pwd", hashedPassword)
                command.Parameters.AddWithValue("@salt", salt)
                command.Parameters.AddWithValue("@id_pg", userId)

                Dim rowsAffected As Integer = command.ExecuteNonQuery()

                If rowsAffected > 0 Then
                    System.Diagnostics.Debug.WriteLine("✅ Password update successful for user: " & userId)
                    Return True
                Else
                    System.Diagnostics.Debug.WriteLine("❌ No rows affected during password update for user: " & userId)
                    Return False
                End If
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Industry standard password update error: " & ex.Message)
            Return False
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

#End Region

#Region "Microservice Integration"

    Private Function SendPasswordChangeNotification() As Boolean
        Try
            If emailClient Is Nothing Then
                System.Diagnostics.Debug.WriteLine("Email service client not available")
                Return False
            End If
            
            Dim userId As String = Session("Id_PG").ToString()
            Dim userEmail As String = GetUserEmail(userId)
            
            If String.IsNullOrEmpty(userEmail) Then
                System.Diagnostics.Debug.WriteLine("User email not found for notifications")
                Return False
            End If
              System.Diagnostics.Debug.WriteLine("MICROSERVICE: Sending password change notification to: " & userEmail)
              ' Send notification via microservice
            Dim response As EmailServiceResponse = emailClient.SendPasswordChangeNotification(userId, userEmail)
            
            If response.Success Then
                System.Diagnostics.Debug.WriteLine("✅ Password change notification sent successfully")
                Return True
            Else
                System.Diagnostics.Debug.WriteLine("❌ Password change notification failed: " & response.Message)
                Return False
            End If
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("❌ Microservice notification error: " & ex.Message)
            Return False
        End Try
    End Function

    Private Function GetUserEmail(userId As String) As String
        Dim connection As OleDbConnection = Nothing
        
        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()
            
            Using command As New OleDbCommand()
                command.Connection = connection
                command.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ?"
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Dim result As Object = command.ExecuteScalar()
                Return If(result IsNot Nothing AndAlso result IsNot DBNull.Value, result.ToString(), "")
            End Using
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Get user email error: " & ex.Message)
            Return ""
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Function

#End Region

#Region "Helper Methods and Data Structures"    ' Data structure for password validation results
    Public Class PasswordValidationResult
        Private _isValid As Boolean
        Private _errors As List(Of String)
        
        Public Property IsValid() As Boolean
            Get
                Return _isValid
            End Get
            Set(ByVal value As Boolean)
                _isValid = value
            End Set
        End Property
        
        Public Property Errors() As List(Of String)
            Get
                Return _errors
            End Get
            Set(ByVal value As List(Of String))
                _errors = value
            End Set
        End Property
        
        Public Sub New()
            _errors = New List(Of String)()
            _isValid = False
        End Sub
    End Class

    Private Function GetSafeStringValue(reader As OleDbDataReader, columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            End If
            Return reader(columnName).ToString()
        Catch
            Return ""
        End Try
    End Function

    Private Function GetSafeBoolValue(reader As OleDbDataReader, columnName As String) As Boolean
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return False
            End If
            Dim value As Object = reader(columnName)
            Return Convert.ToBoolean(value)
        Catch
            Return False
        End Try
    End Function

    Private Sub ShowMessage(message As String, messageType As String)
        messagePanel.Visible = True
        messageLabel.Text = message
        
        Select Case messageType.ToLower()
            Case "success"
                messagePanel.CssClass = "message-container message-success"
            Case "error"
                messagePanel.CssClass = "message-container message-error"
            Case "info"
                messagePanel.CssClass = "message-container message-info"
            Case Else
                messagePanel.CssClass = "message-container message-info"
        End Select
    End Sub
    
    Private Sub HideMessage()
        messagePanel.Visible = False
        messageLabel.Text = ""
    End Sub
    
    Private Sub ClearPasswordFields()
        Tx_CurrentPassword.Text = ""
        Tx_NewPassword.Text = ""
        Tx_ConfirmPassword.Text = ""
    End Sub
    
    Private Sub LogSecurityEvent(eventType As String, userId As String, Optional details As String = "")
        Try
            System.Diagnostics.Debug.WriteLine("SECURITY EVENT: " & eventType & " | User: " & userId & " | Details: " & details & " | Time: " & DateTime.Now.ToString())
            
            ' Optional: Log to database or external security system
            ' LogToSecurityDatabase(eventType, userId, details)
            
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Security logging error: " & ex.Message)
        End Try
    End Sub

#End Region

#Region "Legacy Support Methods"

    ' Legacy method names for backward compatibility
    Private Function ShowClientMessage(message As String, messageType As String) As Boolean
        ShowMessage(message, messageType)
        Return True
    End Function

    Private Function ValidatePasswordStrength(password As String) As PasswordValidationResult
        ' Legacy wrapper for industry standard validation
        Return ValidatePasswordStrengthIndustryStandard(password)
    End Function

    Private Function VerifyCurrentPassword() As Boolean
        ' Legacy wrapper for secure verification
        Return VerifyCurrentPasswordSecure()
    End Function

    Private Function UpdatePasswordSecurely() As Boolean
        ' Legacy wrapper for industry standard update
        Return UpdatePasswordIndustryStandard()
    End Function

    Private Sub LogPasswordChangeEvent(success As Boolean, Optional details As String = "")
        ' Legacy wrapper for security event logging
        Dim eventType As String = If(success, "PASSWORD_CHANGE_SUCCESS", "PASSWORD_CHANGE_FAILED")
        LogSecurityEvent(eventType, Session("Id_PG").ToString(), details)
    End Sub

#End Region

#Region "Email Service Health Check"

    ''' <summary>
    ''' Web method for AJAX email service health check
    ''' </summary>
    <System.Web.Services.WebMethod()>
    Public Shared Function CheckEmailServiceHealth() As String
        Try
            System.Diagnostics.Debug.WriteLine("PN_PWD: Starting email service health check...")

            ' Get email service URL from config
            Dim emailServiceUrl As String = System.Configuration.ConfigurationManager.AppSettings("EmailServiceUrl")
            If String.IsNullOrEmpty(emailServiceUrl) Then
                emailServiceUrl = "http://localhost:5000"
            End If

            Dim testClient As New EmailServiceClient(emailServiceUrl)
            System.Diagnostics.Debug.WriteLine("PN_PWD: Testing connection to: " & emailServiceUrl)

            ' Test the health check
            Dim healthResult As Boolean = testClient.CheckHealth()

            If healthResult Then
                System.Diagnostics.Debug.WriteLine("PN_PWD: ✅ Email service health check successful")
                Return "{""status"":""online"",""message"":""Email Service: Online ✅""}"
            Else
                System.Diagnostics.Debug.WriteLine("PN_PWD: ❌ Email service health check failed")
                Return "{""status"":""offline"",""message"":""Email Service: Offline ⚠️ (Notifications disabled)""}"
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("PN_PWD: ❌ Email service health check error: " & ex.Message)
            Return "{""status"":""offline"",""message"":""Email Service: Offline ⚠️ (Connection error)""}"
        End Try
    End Function

#End Region

#Region "Database Connection Management"

    ''' <summary>
    ''' Test database connection to ensure it's working properly
    ''' </summary>
    Private Sub TestDatabaseConnection()
        Dim connection As OleDbConnection = Nothing

        Try
            connection = New OleDbConnection(ServerId)
            connection.Open()

            ' Test with a simple query
            Using command As New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna WHERE id_pg = ?", connection)
                command.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                Dim result As Object = command.ExecuteScalar()

                If result IsNot Nothing Then
                    System.Diagnostics.Debug.WriteLine("Database connection test successful - User exists: " & (Convert.ToInt32(result) > 0))
                Else
                    Throw New Exception("Database query returned null result")
                End If
            End Using

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Database connection test failed: " & ex.Message)
            Throw New Exception("Database connection failed: " & ex.Message)
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try
    End Sub

#End Region

#Region "Configuration Properties"

    ' Database connection string property - Direct connection without fallbacks
    Private ReadOnly Property ServerId() As String
        Get
            ' Use SPMJ_Mod.ServerId directly - this is the standard connection used throughout the project
            Return SPMJ_Mod.ServerId
        End Get
    End Property

#End Region

End Class