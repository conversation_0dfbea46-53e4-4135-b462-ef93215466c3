<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_XM_JanaKeputusan.aspx.vb" Inherits="SPMJ.WebForm10" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            height: 23px;
        }
    .style3
    {
        height: 12px;
    }
    .style4
    {
        height: 27px;
    }
        .style5
        {
            width: 851px;
        }
        .style6
        {
            height: 28px;
            width: 851px;
        }
        .style7
        {
            height: 23px;
            width: 851px;
        }
        .style8
        {
            height: 12px;
            width: 851px;
        }
        .style9
        {
            height: 27px;
            width: 851px;
        }
        .style10
        {
            width: 87px;
        }
        .style11
        {
            height: 28px;
            width: 87px;
        }
        .style12
        {
            height: 23px;
            width: 87px;
        }
        .style13
        {
            height: 12px;
            width: 87px;
        }
        .style14
        {
            height: 27px;
            width: 87px;
        }
    </style></asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style10"></td>
            <td class="style5"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style10">&nbsp;</td>
            <td class="style5">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style11"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#BF0000" class="style6">Jana Keputusan Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style5">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="White" class="style5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">JENIS PEPERIKSAAN</asp:TextBox>
                <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="250px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">KEBIDANAN I</asp:ListItem>
        </asp:DropDownList>
                <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SEMAK" Width="60px" />
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style12"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="White" class="style7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">MARKAH LULUS</asp:TextBox>
                <asp:TextBox ID="Tx_Lulus" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                <asp:Button ID="cmd_Jana" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="60px" />
            &nbsp;&nbsp;&nbsp;
                <asp:Button ID="cmd_excel" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXCEL" Width="60px" />
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style12"></td>
            <td 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="White" class="style7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <asp:TextBox ID="TextBox2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="412px"></asp:TextBox>
                <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                    BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                    Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="137px" Visible="False">NOTA : (*) CONVERSION</asp:TextBox>
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style13"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="White" class="style8">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>
        <tr valign="bottom">
            <td class="style14"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="#EEEEEE" class="style9" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                         <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="170px" Enabled="False" 
                            ReadOnly="True">JUMLAH CALON KESELURUHAN</asp:TextBox>
                <asp:TextBox ID="Tx_Calon" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                    
                </td>
            <td class="style4"></td>
        </tr>
       
         <tr>
            <td class="style12">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="#EEEEEE" class="style7" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    
                    <asp:TextBox ID="TextBox4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="171px" Enabled="False" 
                            ReadOnly="True">JUMLAH CALON MENDUDUKI</asp:TextBox>
                <asp:TextBox ID="Tx_Menduduki" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                    &nbsp;&nbsp;<asp:TextBox ID="TextBox3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="123px" Enabled="False" 
                            ReadOnly="True">JUMLAH TIDAK HADIR</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_T" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>     </td>
            <td class="style2">&nbsp;</td>
        </tr>
      <tr>
            <td class="style12">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F" 
                bgcolor="#EEEEEE" class="style7" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="171px" Enabled="False" 
                            ReadOnly="True">JUMLAH LULUS</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_L" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="99px" Enabled="False" 
                            ReadOnly="True">JUMLAH GAGAL</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_G" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                       
                </td>
            <td class="style2">&nbsp;</td>
        </tr>
         
        <tr>
            <td class="style10">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #6F006F; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="#EEEEEE" class="style5">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td class="style5">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td class="style5">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="9pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" Visible="False" BorderColor="#BF0000">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="White" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#BF0000" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td></td>
        </tr>
    <tr>
            <td class="style10"></td>
            <td class="style5">&nbsp;</td>
            <td></td>
        </tr>        
        <tr>
            <td class="style10">&nbsp;</td>
            <td class="style5">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
