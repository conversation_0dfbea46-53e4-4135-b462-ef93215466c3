﻿<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="Vw_Pelatih.aspx.vb" Inherits="SPMJ.Vw_Pelatih" %>

<%@ Register assembly="Microsoft.ReportViewer.WebForms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" namespace="Microsoft.Reporting.WebForms" tagprefix="rsweb" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
    <style type="text/css">
        .style1
        {
            width: 171px;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
    <div align="center">
    
        <table cellpadding="-1" cellspacing="-1" 
            style="width: 100%; font-family: Arial; font-size: 8PT; font-weight: bold; font-variant: small-caps;">
            <tr>
                <td class="style1">
                    NAMA</td>
                <td>
                    NO. KP/ PASPORT</td>
                <td>
                    KOLEJ</td>
            </tr>
            <tr>
                <td class="style1">
                    &nbsp;</td>
                <td>
                    &nbsp;</td>
                <td>
                    &nbsp;</td>
            </tr>
            <tr>
                <td class="style1">
                    &nbsp;</td>
                <td>
                    &nbsp;</td>
                <td>
                    &nbsp;</td>
            </tr>
        </table>
    
    </div>
    <div>
        <asp:TextBox ID="TextBox1" runat="server" Height="118px" TextMode="MultiLine" 
            Width="100%">&lt;table cellpadding=&quot;-1&quot; cellspacing=&quot;-1&quot; style=&quot;width: 100%; 
        font-family: Arial; font-size: 8PT; font-weight: bold; font-variant: 
        small-caps;&quot;&gt; &lt;tr&gt; &lt;td class=&quot;style1&quot;&gt; NAMA&lt;/td&gt; &lt;td&gt; NO. KP/ PASPORT&lt;/td&gt; &lt;td&gt; 
        KOLEJ&lt;/td&gt; &lt;/tr&gt;</asp:TextBox>
        <br />
        <asp:TextBox ID="TextBox2" runat="server" Height="118px" TextMode="MultiLine" 
            Width="100%"> &lt;/table&gt;</asp:TextBox>
        <br />
        <asp:Button ID="Button1" runat="server" Text="Button" />
    </div>
    </form>
</body>
</html>
