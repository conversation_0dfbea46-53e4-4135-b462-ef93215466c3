﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P5_Cetak.aspx.vb" Inherits="SPMJ.WebForm45" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">
<!--
window.history.forward(1);
// -->
</script>
    <style type="text/css">
        .style2
        {
            height: 64px;
        }
        .style5
        {
        }
        .style8
        {
            width: 137px;
            height: 64px;
        }
        .style14
        {
            height: 64px;
            width: 123px;
        }
        .style15
        {
            height: 21px;
            width: 123px;
        }
        .style16
        {
            width: 123px;
        }
        .style37
        {
            height: 24px;
        }
        .style38
        {
            width: 127px;
        }
        .style40
        {
            width: 137px;
        }
        .style41
        {
            width: 137px;
            height: 21px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr style="width: 10%">
            <td width="150" rowspan="10"></td>
            <td width="500" colspan="2">
                &nbsp;</td>
            <td width="150" rowspan="10">&nbsp;</td>
        </tr>
        <tr style="width: 10%">
            <td width="500" colspan="2">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
        </tr>
        <tr>
            <td colspan="2" align="center" bgcolor="#719548" class="style37" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;">
                Cetak Surat Notis Niat</td>
        </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style96" 
                                         
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px;">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" 
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-right-style: solid; border-right-width: 1px;">
                            &nbsp;</td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style2" 
                                         
                                         style="border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px;" 
                                         colspan="2">
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                <ContentTemplate>
                                    <table cellpadding="-1" cellspacing="-1" 
    
                                        style="border-color: #7EA851; width:100%; border-bottom-style: solid; border-bottom-width: 1px; border-right-width: 1px; border-right-style: Solid;">
                                        <tr>
                                            <td class="style38" width="100px" rowspan="13">
                                                &nbsp;</td>
                                            <td class="style41">
                                                &nbsp; TEMPAT AMALAN&nbsp;</td>
                                            <td class="style15" width="280px">
                                                <asp:DropDownList ID="Cb_Tpt_Amalan" runat="server" AutoPostBack="True" 
                                                    CssClass="std" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                    style="margin-left: 0px" Width="256px">
                                                </asp:DropDownList>
                                            </td>
                                            <td class="style5" width="100px" rowspan="13">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style8" valign="top">
                                                &nbsp; ALAMAT AMALAN</td>
                                            <td class="style14">
                                                <asp:TextBox ID="Tx_Amalan_Alamat" runat="server" CssClass="std" Height="60px" 
                                                    ReadOnly="True" TextMode="MultiLine" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; POSKOD</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Poskod" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; BANDAR</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Bandar" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; NEGERI</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Negeri" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; NO. TELEFON</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Tel" runat="server" CssClass="std" ReadOnly="True" 
                                                    Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; NO. FAKS</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Fax" runat="server" CssClass="std" ReadOnly="True" 
                                                    Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; SEKTOR</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_Amalan_Sektor" runat="server" CssClass="std" 
                                                    ReadOnly="True" Width="250px"></asp:TextBox>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp;</td>
                                            <td class="style15">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp; TARIKH NOTIS NIAT</td>
                                            <td class="style15">
                                                <asp:TextBox ID="Tx_TkhResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                <cc1:MaskedEditExtender ID="Tx_TkhResit_MaskedEditExtender" runat="server" 
                                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_TkhResit" 
                                                    UserDateFormat="DayMonthYear">
                                                </cc1:MaskedEditExtender>
                                                <cc1:CalendarExtender ID="Tx_TkhResit_CalendarExtender" runat="server" 
                                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                    TargetControlID="Tx_TkhResit">
                                                </cc1:CalendarExtender>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp;</td>
                                            <td class="style15">
                                                &nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td class="style41">
                                                &nbsp;</td>
                                            <td class="style15">
                                                <asp:Button ID="cmdHantar0" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                    Height="20px" tabIndex="3" Text="CARI" Width="96px" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="style40">
                                                &nbsp;</td>
                                            <td class="style16">
                                                &nbsp;</td>
                                        </tr>
                                    </table>
                                </ContentTemplate>
                            </asp:UpdatePanel>                            <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                            </asp:UpdatePanel>
                                     </td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style96" 
                                         
                                         
                                         style="font-family: Arial; font-size: 8pt; font-variant: small-caps; border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px; border-right-style: solid; border-right-width: 1px;" 
                                         align="center" colspan="2">
                            <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                                <ContentTemplate>
                                    <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#719548">
                                        <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                                        <RowStyle BackColor="#F4F4F4" Height="21px" HorizontalAlign="Left" />
                                        <Columns>
                                            <asp:TemplateField HeaderText="#">
                                                <HeaderStyle HorizontalAlign="Center" />
                                                <ItemStyle HorizontalAlign="Center" Width="30px" />
                                            </asp:TemplateField>
                                        </Columns>
                                        <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                        <SelectedRowStyle Font-Bold="False" />
                                        <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                        <AlternatingRowStyle BackColor="White" />
                                    </asp:GridView>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                                     </td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style96" 
                                         
                                         
                                         style="font-family: Arial; font-size: 8pt; font-variant: small-caps; border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px; border-right-style: solid; border-right-width: 1px;" 
                                         align="center" colspan="2">
                            &nbsp;</td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style96" 
                                         
                                         
                                         style="font-family: Arial; font-size: 8pt; font-variant: small-caps; border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px; border-right-style: solid; border-right-width: 1px;" 
                                         align="center" colspan="2">
                <asp:Button ID="cmdCetak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK SURAT" Width="96px" />
                                     </td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style96" 
                                         
                                         
                                         style="font-family: Arial; font-size: 8pt; font-variant: small-caps; border-color: #7EA851; font-family: Arial; font-size: 8pt; font-variant: small-caps; border-left-style: solid; border-left-width: 1px; border-right-style: solid; border-right-width: 1px; border-bottom-style: solid; border-bottom-width: 1px;" 
                                         colspan="2">
                            &nbsp;</td>
                    </tr>
<tr>
            <td width="600" colspan="2">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>