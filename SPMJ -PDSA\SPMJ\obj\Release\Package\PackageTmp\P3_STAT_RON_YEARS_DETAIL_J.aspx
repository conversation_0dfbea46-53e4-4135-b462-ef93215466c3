﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_STAT_RON_YEARS_DETAIL_J.aspx.vb" Inherits="SPMJ.P3_STAT_RON_YEARS_DETAIL_J" 
    title="LAPORAN PENGEKALAN NAMA BULANAN BERDASARKAN TAHUN PENGEKALAN " %>
 <asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
     <style type="text/css">
        .style2
        {
            height: 7px;
        }
        .style7
        {
            width: 427px;
        }
        .style9
        {
            height: 7px;
            width: 427px;
        }
        .style10
        {
            height: 14px;
        }
        .style11
        {
            height: 14px;
            width: 427px;
        }
        .style12
        {
            height: 37px;
        }
        .style13
        {
            width: 427px;
            height: 37px;
        }
        .style14
        {
            height: 41px;
        }
        .style15
        {
            width: 427px;
            height: 41px;
        }
    </style>
    <script src="http://ajax.aspnetcdn.com/ajax/jQuery/jquery-1.10.0.min.js" type="text/javascript"></script>
<script src="http://ajax.aspnetcdn.com/ajax/jquery.ui/1.9.2/jquery-ui.min.js" type="text/javascript"></script>
<link href="http://ajax.aspnetcdn.com/ajax/jquery.ui/1.9.2/themes/blitzer/jquery-ui.css" rel="Stylesheet" type="text/css" />

<%--<script src="/scripts/jquery-ui.js" type="text/javascript"></script>
<script src="/scripts/jquery-ui.min.js" type="text/javascript"></script>
<link href="/styles/jquery-ui.css" rel="Stylesheet" type="text/css" />--%>
    <script type="text/javascript">
        // if you use jQuery, you can load them when dom is read.
        $(document).ready(function() {
            var prm = Sys.WebForms.PageRequestManager.getInstance();
            prm.add_initializeRequest(InitializeRequest);
            prm.add_endRequest(EndRequest);

            // Place here the first init of the DatePicker
            $(".std_date").datepicker({ dateFormat: 'dd/mm/yy' });
        });

        function InitializeRequest(sender, args) {
            // make unbind to avoid memory leaks.
            $(".std_date").unbind({ dateFormat: 'dd/mm/yy' });
        }

        function EndRequest(sender, args) {
            // after update occur on UpdatePanel re-init the DatePicker
            $(".std_date").datepicker({ dateFormat: 'dd/mm/yy' });
        }
</script>

<script type="text/javascript">
  //Total out of range dialog
  function ShowInfoDialog() {
    $(function() {
      $('#infoDialog').dialog({
        modal: true,
//        width: 'auto',
        //width: '35px',
        resizable: false,
        draggable: false,
        close: function (event, ui) { $('body').find('#infoDialog').remove(); },
        buttons: { 'OK': function () { $(this).dialog('close'); }
        }
      })
    }).dialog("open");
  }
</script>

<div id="infoDialog" style="display: none;" title="PERHATIAN - Tiada Tarikh!!!">
  <p>
   Sila pilih tarikh proses pengekalan mula dan tamat yang betul, sebelum tekan butang "JANA". 
  </p>
</div>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td class="style7"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style14"></td>
            <td align="center" 
                style="border-style: solid; border-width: 1px; border-color: #000000 #000000 #808055 #000000; font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold; background-color: #999966;" 
                bgcolor="#999966" class="style15">LAPORAN STATISTIK PENGEKALAN NAMA
                <br />
                bulanan BERDASARKAN TAHUN PENGEKALAN</td>
            <td class="style14"></td>
        </tr>
        <tr>
            <td></td>
            <td
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style7">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td                 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj11" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="81px" Enabled="False" 
                            ReadOnly="True">TARIKH MULA</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_M" runat="server" CssClass="std_date" Width="95px"></asp:TextBox>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj12" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="81px" Enabled="False" 
                            ReadOnly="True">TARIKH TAMAT</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_A" runat="server" CssClass="std_date" Width="95px"></asp:TextBox>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style11">
                </td>
            <td class="style10"></td>
        </tr>
        <tr>
            <td class="style12"></td>
            <td 
                
                style="border-style: none solid solid solid; border-width: 1px; border-color: #000000 #003300 #003300 #003300; background-color: #D7D7C4;" 
                bgcolor="White" class="style13" align="right">
                &nbsp;&nbsp;<asp:Button ID="cmd_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
            <td class="style12"></td>
        </tr>
        <tr>
            <td></td>
            <td class="style7">&nbsp;</td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td class="style7">
                <br />
                        <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    </table>
    </div></asp:Content>