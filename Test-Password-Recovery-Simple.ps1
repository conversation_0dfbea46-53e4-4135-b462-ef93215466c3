# Simple Password Recovery Test
Write-Host "=== Password Recovery Function Test ===" -ForegroundColor Green

$microserviceUrl = "http://localhost:5000"
$apiKey = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
$testUserId = "820228115693"
$testEmail = "<EMAIL>"
$testUserName = "SUHANG"
$tempPassword = "TempPass123!"

$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = $apiKey
}

Write-Host "Testing password recovery endpoint..." -ForegroundColor Yellow

$sendRequest = @{
    UserId = $testUserId
    Email = $testEmail
    UserName = $testUserName
    TempPassword = $tempPassword
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$microserviceUrl/api/password/reset/send" -Method POST -Body $sendRequest -Headers $headers
    
    if ($response.success) {
        Write-Host "✅ Password recovery email sent successfully!" -ForegroundColor Green
        Write-Host "   Message: $($response.message)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "🎯 The p0_Login.aspx password recovery is now FIXED!" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "What was fixed:" -ForegroundColor Yellow
        Write-Host "• Added missing /api/password/reset/send endpoint" -ForegroundColor Green
        Write-Host "• Added SendPasswordResetEmailRequest model" -ForegroundColor Green
        Write-Host "• Added password recovery email service method" -ForegroundColor Green
        Write-Host "• Updated service interfaces" -ForegroundColor Green
        Write-Host ""
        Write-Host "Test the fix:" -ForegroundColor Yellow
        Write-Host "1. Go to p0_Login.aspx" -ForegroundColor Gray
        Write-Host "2. Click 'Lupa Kata Laluan?' link" -ForegroundColor Gray
        Write-Host "3. Enter User ID: $testUserId" -ForegroundColor Gray
        Write-Host "4. Click 'Hantar' button" -ForegroundColor Gray
        Write-Host "5. Check email for temporary password" -ForegroundColor Gray
    } else {
        Write-Host "❌ Password recovery failed: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Request failed: $($_.Exception.Message)" -ForegroundColor Red
}
