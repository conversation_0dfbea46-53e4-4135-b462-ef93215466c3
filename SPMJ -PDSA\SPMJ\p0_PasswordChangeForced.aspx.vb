Imports System.Data.OleDb
Imports System.Web.UI.WebControls
Imports System.Data

Partial Public Class p0_PasswordChangeForced
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        ' Debug session state for troubleshooting
        System.Diagnostics.Debug.WriteLine("=== p0_PasswordChangeForced Session Debug ===")
        System.Diagnostics.Debug.WriteLine("Session ID_PG: " & If(Session("Id_PG") Is Nothing, "NULL", Session("Id_PG").ToString()))
        System.Diagnostics.Debug.WriteLine("Session FORCE_PASSWORD_CHANGE: " & If(Session("FORCE_PASSWORD_CHANGE") Is Nothing, "NULL", Session("FORCE_PASSWORD_CHANGE").ToString()))
        
        ' Check if user is properly authenticated and forced password change is required
        ' FIXED: Proper null-safe session validation
        If Session("Id_PG") Is Nothing OrElse Session("Id_PG").ToString().Trim() = "" Or Session("FORCE_PASSWORD_CHANGE") <> "true" Then
            System.Diagnostics.Debug.WriteLine("❌ Session validation failed - redirecting to login")
            Session.Abandon()
            Response.Redirect("p0_Login.aspx")
            Exit Sub
        End If
        
        System.Diagnostics.Debug.WriteLine("✅ Session validation passed - loading password change form")
        
        ' Hide menu for this page
        Dim x As Menu = Master.FindControl("Menu1")
        If x IsNot Nothing Then x.Visible = False
    End Sub

    Protected Sub cmd_Update_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Update.Click
        If Tx_NewPwd.Text.Trim = "" Then 
            Msg(Me, "Sila masukkan kata laluan baru!")
            Tx_NewPwd.Focus() 
            Exit Sub
        End If
        
        If Tx_NewPwd.Text.Length < 6 Then
            Msg(Me, "Kata laluan mestilah sekurang-kurangnya 6 aksara!")
            Tx_NewPwd.Focus()
            Exit Sub
        End If
        
        If Tx_ConfirmPwd.Text.Trim = "" Then 
            Msg(Me, "Sila sahkan kata laluan baru!")
            Tx_ConfirmPwd.Focus() 
            Exit Sub
        End If
        
        If Tx_NewPwd.Text <> Tx_ConfirmPwd.Text Then
            Msg(Me, "Kata laluan baru dan pengesahan tidak sepadan!")
            Tx_ConfirmPwd.Focus()
            Exit Sub
        End If
        
        ' Validate for SQL injection
        If Chk_SQL(Tx_NewPwd.Text) = True Then 
            Msg(Me, "Kata laluan mengandungi aksara tidak dibenarkan!")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
          Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            
            ' Create encrypted password with salt
            Dim passwordEntry As String() = PasswordHelper.CreatePasswordEntry(Tx_NewPwd.Text)
            Dim hashedPassword As String = passwordEntry(0)
            Dim salt As String = passwordEntry(1)

            ' Debug: Check lengths to ensure they fit in database columns
            If hashedPassword.Length > 100 Then
                Msg(Me, "Error: Hashed password too long (" & hashedPassword.Length & " chars). Please contact administrator.")
                Exit Sub
            End If
            
            If salt.Length > 100 Then
                Msg(Me, "Error: Salt too long (" & salt.Length & " chars). Please contact administrator.")
                Exit Sub
            End If            ' Update password in database and clear temporary password flags
            Cmd.CommandText = "UPDATE pn_pengguna SET pwd = ?, salt = ?, password_migrated = 1, is_temporary = 0, force_change = 0, tarikh_tukar_katalaluan = ? WHERE status = 1 and id_pg = ?"
            Cmd.Parameters.Clear()
            
            ' Use explicit parameter configuration to prevent truncation
            Dim pwdParam As New OleDbParameter("@pwd", OleDbType.VarChar, 100)
            pwdParam.Value = hashedPassword
            Cmd.Parameters.Add(pwdParam)
            
            Dim saltParam As New OleDbParameter("@salt", OleDbType.VarChar, 100)
            saltParam.Value = salt
            Cmd.Parameters.Add(saltParam)
            
            Dim dateParam As New OleDbParameter("@date", OleDbType.Date)
            dateParam.Value = DateTime.Now
            Cmd.Parameters.Add(dateParam)
              Dim idParam As New OleDbParameter("@id_pg", OleDbType.VarChar, 50)
            idParam.Value = Session("Id_PG").ToString()
            Cmd.Parameters.Add(idParam)
            
            Dim rowsAffected As Integer = Cmd.ExecuteNonQuery()
            
            ' Debug: Log password change result
            System.Diagnostics.Debug.WriteLine("=== Password Change Debug ===")
            System.Diagnostics.Debug.WriteLine("User ID: " & Session("Id_PG").ToString())
            System.Diagnostics.Debug.WriteLine("Rows affected: " & rowsAffected.ToString())
            System.Diagnostics.Debug.WriteLine("Password hash length: " & hashedPassword.Length.ToString())
            System.Diagnostics.Debug.WriteLine("Salt length: " & salt.Length.ToString())
              If rowsAffected > 0 Then
                ' Verify the update was successful by reading the flags back
                Cmd.CommandText = "SELECT is_temporary, force_change, password_migrated FROM pn_pengguna WHERE id_pg = ?"
                Cmd.Parameters.Clear()
                Cmd.Parameters.AddWithValue("@id_pg", Session("Id_PG").ToString())
                
                Dim verifyReader As OleDbDataReader = Cmd.ExecuteReader()
                If verifyReader.Read() Then
                    System.Diagnostics.Debug.WriteLine("Password Change Verification:")
                    System.Diagnostics.Debug.WriteLine("is_temporary: " & verifyReader("is_temporary").ToString())
                    System.Diagnostics.Debug.WriteLine("force_change: " & verifyReader("force_change").ToString())
                    System.Diagnostics.Debug.WriteLine("password_migrated: " & verifyReader("password_migrated").ToString())
                End If
                verifyReader.Close()
                
                ' Clear the forced password change flag
                Session("FORCE_PASSWORD_CHANGE") = Nothing
                
                ' Show success message and redirect
                Msg(Me, "Kata laluan telah berjaya dikemaskini! Anda akan dialihkan ke halaman utama.")
                
                ' Add a small delay and redirect using JavaScript
                ClientScript.RegisterStartupScript(Me.GetType(), "redirect", "setTimeout(function(){ window.location.href='blank.aspx'; }, 2000);", True)
            Else
                Msg(Me, "Ralat semasa mengemaskini kata laluan. Sila cuba lagi.")
            End If
            
        Catch ex As Exception
            Msg(Me, "Ralat sistem: " & ex.Message)
        Finally
            If Cn.State = ConnectionState.Open Then Cn.Close()
        End Try
    End Sub

    Protected Sub cmd_Logout_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Logout.Click
        Session.Abandon()
        Response.Redirect("p0_Login.aspx")
    End Sub
End Class
