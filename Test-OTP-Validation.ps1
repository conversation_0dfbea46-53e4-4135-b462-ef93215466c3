# Test OTP Validation with previously generated code
Write-Host "=== Testing OTP Validation ===" -ForegroundColor Green

$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
}

$validateRequest = @{
    UserId = '820228115693'
    OtpCode = '883318'
    Purpose = 'LOGIN'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/otp/validate' -Method POST -Body $validateRequest -Headers $headers
    Write-Host "Validation Success: $($response.success)" -ForegroundColor Green
    Write-Host "Message: $($response.message)" -ForegroundColor Gray
    if ($response.data) {
        Write-Host "Data: $($response.data)" -ForegroundColor Gray
    }
} catch {
    Write-Host "Validation Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
        $reader.Close()
        $stream.Close()
    }
}

Write-Host ""
Write-Host "Testing invalid OTP..." -ForegroundColor Yellow

$invalidRequest = @{
    UserId = '820228115693'
    OtpCode = '999999'
    Purpose = 'LOGIN'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/otp/validate' -Method POST -Body $invalidRequest -Headers $headers
    Write-Host "Invalid OTP Success: $($response.success)" -ForegroundColor Yellow
    Write-Host "Message: $($response.message)" -ForegroundColor Gray
} catch {
    Write-Host "Invalid OTP properly rejected" -ForegroundColor Green
}
