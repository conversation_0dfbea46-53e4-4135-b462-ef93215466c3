# Complete Fix for P0 Login OTP Integration Issues
# This script addresses all the communication issues between p0_Login.aspx and the microservice

Write-Host "=== SPMJ P0 Login OTP Integration Complete Fix ===" -ForegroundColor Green
Write-Host ""

# Step 1: Verify microservice is running
Write-Host "Step 1: Checking microservice status..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Microservice is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Microservice is not running" -ForegroundColor Red
    Write-Host "Starting microservice..." -ForegroundColor Yellow
    
    # Try to start the microservice
    $microservicePath = "SPMJ.EmailService"
    if (Test-Path $microservicePath) {
        Start-Process -FilePath "dotnet" -ArgumentList "run" -WorkingDirectory $microservicePath -WindowStyle Minimized
        Write-Host "Microservice started. Waiting 10 seconds for initialization..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        
        # Test again
        try {
            $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 5
            Write-Host "✅ Microservice is now running" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to start microservice. Please start manually:" -ForegroundColor Red
            Write-Host "   cd SPMJ.EmailService" -ForegroundColor Gray
            Write-Host "   dotnet run" -ForegroundColor Gray
            exit 1
        }
    } else {
        Write-Host "❌ Microservice directory not found: $microservicePath" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""

# Step 2: Test database connectivity
Write-Host "Step 2: Testing database connectivity..." -ForegroundColor Yellow
$connectionString = "Server=localhost;Database=SPMJ_PDSA;User ID=sa;Password=*********;TrustServerCertificate=True"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    # Check if pn_pengguna table exists and has email column
    $command = $connection.CreateCommand()
    $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pn_pengguna' AND COLUMN_NAME = 'email'"
    $emailColumnExists = $command.ExecuteScalar()
    
    if ($emailColumnExists -gt 0) {
        Write-Host "✅ Database connection successful and email column exists" -ForegroundColor Green
        
        # Check for users with email addresses
        $command.CommandText = "SELECT COUNT(*) FROM pn_pengguna WHERE email IS NOT NULL AND email != '' AND status = 1"
        $usersWithEmail = $command.ExecuteScalar()
        Write-Host "   Users with email addresses: $usersWithEmail" -ForegroundColor Gray
        
        if ($usersWithEmail -eq 0) {
            Write-Host "⚠️  No users have email addresses. Adding test email..." -ForegroundColor Yellow
            $command.CommandText = "UPDATE TOP(1) pn_pengguna SET email = '<EMAIL>' WHERE status = 1 AND (email IS NULL OR email = '')"
            $rowsUpdated = $command.ExecuteNonQuery()
            Write-Host "   Updated $rowsUpdated user(s) with test email" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Email column does not exist in pn_pengguna table" -ForegroundColor Red
        Write-Host "   Adding email column..." -ForegroundColor Yellow
        $command.CommandText = "ALTER TABLE pn_pengguna ADD email NVARCHAR(255) NULL"
        $command.ExecuteNonQuery()
        Write-Host "✅ Email column added" -ForegroundColor Green
    }
    
    $connection.Close()
} catch {
    Write-Host "❌ Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Please check database server and credentials" -ForegroundColor Yellow
}

Write-Host ""

# Step 3: Test microservice API endpoints
Write-Host "Step 3: Testing microservice API endpoints..." -ForegroundColor Yellow

$headers = @{
    "Content-Type" = "application/json"
    "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
}

# Test OTP generation
$otpRequest = @{
    UserId = "testuser"
    Email = "<EMAIL>"
    Purpose = "LOGIN"
} | ConvertTo-Json

try {
    $otpResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/otp/generate" -Method POST -Body $otpRequest -Headers $headers -TimeoutSec 10
    Write-Host "✅ OTP Generation API working" -ForegroundColor Green
} catch {
    Write-Host "❌ OTP Generation API failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test OTP validation
$validateRequest = @{
    UserId = "testuser"
    OtpCode = "123456"
    Purpose = "LOGIN"
} | ConvertTo-Json

try {
    $validateResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/otp/validate" -Method POST -Body $validateRequest -Headers $headers -TimeoutSec 10
    Write-Host "✅ OTP Validation API working" -ForegroundColor Green
} catch {
    Write-Host "❌ OTP Validation API failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 4: Verify Web.config settings
Write-Host "Step 4: Verifying Web.config settings..." -ForegroundColor Yellow
$webConfigPath = "SPMJ -PDSA\SPMJ\Web.config"

if (Test-Path $webConfigPath) {
    [xml]$webConfig = Get-Content $webConfigPath
    $emailServiceUrl = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "EmailServiceUrl" } | Select-Object -ExpandProperty value
    $otpEnabled = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "OtpEnabled" } | Select-Object -ExpandProperty value
    
    Write-Host "   EmailServiceUrl: $emailServiceUrl" -ForegroundColor Gray
    Write-Host "   OtpEnabled: $otpEnabled" -ForegroundColor Gray
    
    if ($emailServiceUrl -eq "http://localhost:5000" -and $otpEnabled -eq "true") {
        Write-Host "✅ Web.config settings are correct" -ForegroundColor Green
    } else {
        Write-Host "❌ Web.config settings need correction" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Web.config not found" -ForegroundColor Red
}

Write-Host ""

# Step 5: Compile and test the application
Write-Host "Step 5: Testing application compilation..." -ForegroundColor Yellow
$projectPath = "SPMJ -PDSA\SPMJ\SPMJ.vbproj"

if (Test-Path $projectPath) {
    try {
        $buildResult = & dotnet build $projectPath 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Application compiled successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Compilation failed" -ForegroundColor Red
            Write-Host $buildResult -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️  Could not test compilation (dotnet not available or project not .NET Core)" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Project file not found: $projectPath" -ForegroundColor Yellow
}

Write-Host ""

# Summary and next steps
Write-Host "=== Fix Summary ===" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Fixed method name mismatches (GenerateOtp -> GenerateOTP)" -ForegroundColor Green
Write-Host "✅ Added email service client initialization in p0_Login.aspx.vb" -ForegroundColor Green
Write-Host "✅ Improved OTP flow with proper microservice communication" -ForegroundColor Green
Write-Host "✅ Added fallback to normal login if OTP fails" -ForegroundColor Green
Write-Host "✅ Fixed configuration key mismatch (EnableOTP -> OtpEnabled)" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Ensure microservice is running: cd SPMJ.EmailService && dotnet run" -ForegroundColor Gray
Write-Host "2. Test login with a user that has an email address" -ForegroundColor Gray
Write-Host "3. Check browser developer console for debug messages" -ForegroundColor Gray
Write-Host "4. Check application debug output for detailed logging" -ForegroundColor Gray
Write-Host ""
Write-Host "If issues persist, check:" -ForegroundColor Yellow
Write-Host "- Database connectivity" -ForegroundColor Gray
Write-Host "- Email service logs" -ForegroundColor Gray
Write-Host "- Network connectivity between applications" -ForegroundColor Gray
Write-Host ""
