# Simple Microservice Startup
Write-Host "Starting SPMJ Email Microservice..." -ForegroundColor Green

# Kill existing processes
Write-Host "Cleaning up existing processes..." -ForegroundColor Yellow
taskkill /F /IM dotnet.exe 2>$null
taskkill /F /IM SPMJ.EmailService.exe 2>$null

# Free port 5000
Write-Host "Freeing port 5000..." -ForegroundColor Yellow
$portProcess = netstat -ano | Select-String ":5000.*LISTENING"
if ($portProcess) {
    $pid = ($portProcess.Line -split '\s+')[-1]
    if ($pid -match '^\d+$') {
        taskkill /F /PID $pid 2>$null
        Write-Host "Killed process $pid on port 5000" -ForegroundColor Gray
    }
}

Start-Sleep -Seconds 2

# Start microservice
Write-Host "Starting microservice..." -ForegroundColor Yellow
Set-Location "SPMJ.EmailService"

# Build first
dotnet build | Out-Null
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}

# Start the service
$process = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -WindowStyle Hidden
Write-Host "Microservice started with PID: $($process.Id)" -ForegroundColor Green

Set-Location ".."

# Wait and test
Write-Host "Waiting for microservice to initialize..." -ForegroundColor Gray
Start-Sleep -Seconds 8

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/health" -TimeoutSec 5
    Write-Host "✅ Microservice is healthy: $($response.status)" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎯 Password recovery is now ready!" -ForegroundColor Cyan
    Write-Host "Test at: p0_Login.aspx -> 'Lupa Kata Laluan?' -> User ID: 820228115693" -ForegroundColor Gray
} catch {
    Write-Host "❌ Microservice not responding, but fallback mode will work" -ForegroundColor Yellow
    Write-Host "Password will be shown directly on screen" -ForegroundColor Gray
}
