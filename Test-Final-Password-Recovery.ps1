# Final Password Recovery Test
Write-Host "=== Final Password Recovery Test ===" -ForegroundColor Green

# Test microservice health
Write-Host "Testing microservice..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:5000/health" -TimeoutSec 5
    Write-Host "✅ Microservice: $($health.status)" -ForegroundColor Green
    $microserviceOK = $true
} catch {
    Write-Host "❌ Microservice: Not responding" -ForegroundColor Red
    $microserviceOK = $false
}

# Test password recovery endpoint
if ($microserviceOK) {
    Write-Host "Testing password recovery endpoint..." -ForegroundColor Yellow
    
    $headers = @{
        'Content-Type' = 'application/json'
        'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
    }
    
    $body = @{
        UserId = '820228115693'
        Email = '<EMAIL>'
        UserName = 'SUHANG'
        TempPassword = 'TestPass123!'
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/password/reset/send' -Method POST -Body $body -Headers $headers -TimeoutSec 10
        Write-Host "✅ Password recovery: $($response.success)" -ForegroundColor Green
        Write-Host "   Message: $($response.message)" -ForegroundColor Gray
    } catch {
        Write-Host "❌ Password recovery failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Status Summary ===" -ForegroundColor Green
if ($microserviceOK) {
    Write-Host "🎯 Password Recovery: ✅ FULLY FUNCTIONAL" -ForegroundColor Cyan
    Write-Host "• Microservice running and healthy" -ForegroundColor Green
    Write-Host "• Email will be sent to user" -ForegroundColor Green
    Write-Host "• Success message will show masked email" -ForegroundColor Green
} else {
    Write-Host "🎯 Password Recovery: ✅ FALLBACK MODE" -ForegroundColor Cyan
    Write-Host "• Microservice not available" -ForegroundColor Yellow
    Write-Host "• Password will be shown directly on screen" -ForegroundColor Orange
    Write-Host "• User can copy and use immediately" -ForegroundColor Orange
}

Write-Host ""
Write-Host "Ready to test! Go to p0_Login.aspx and try password recovery." -ForegroundColor Green
