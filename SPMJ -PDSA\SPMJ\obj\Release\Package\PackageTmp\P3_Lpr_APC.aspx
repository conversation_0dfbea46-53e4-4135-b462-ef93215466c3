﻿<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="P3_Lpr_APC.aspx.vb" Inherits="SPMJ.Lpr_P4_APC" %>

<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Untitled Page</title>
    <style type="text/css">

        .menu_small
        {
             font-family: Arial;
             font-size: 7pt; 
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style1
        {
            font-family: Arial;
            font-size: 18px;
            color: #669900;
        }
        .style2
        {
            height: 7px;
        }
        </style>
</head>
<body>
    <form id="form1" runat="server">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManager ID="ScriptManager1" runat="server">
                </asp:ScriptManager>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">Statistik Pengeluaran APC</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj17" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TARIKH APC</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh" runat="server" CssClass="std" 
            Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh">
                        </cc1:CalendarExtender>
                        <asp:TextBox ID="Cb_Sbj16" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">-</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh2" runat="server" CssClass="std" 
            Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh2_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" 
                            TargetControlID="Tx_Tkh2" UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh2_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh2">
                        </cc1:CalendarExtender>
                        <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;<asp:Button ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK" Width="80px" />
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                        &nbsp;</td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="False" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
    
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </form>
</body>
</html>
