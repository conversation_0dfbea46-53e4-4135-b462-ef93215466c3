﻿<%@ Page Title="" Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P4_Pre_JTWA.aspx.vb" Inherits="SPMJ.P4_PRE_JTWA" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">
<!--
window.history.forward(1);
// -->
</script>    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style9
        {
            height: 23px;
        }
        .style12
        {
            height: 21px;
        }
        .style33
    {
            height: 20px;
        }
        p.<PERSON>
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style62
        {
                     height: 24px;
                     width: 247px;
                 }
        .style81
        {
            width: 650px;
            height: 436px;
            left: 0px;
            top: 70px;
            position: static;
        }
        .style83
        {
                     height: 20px;
                     width: 247px;
                 }
        .style87
        {
                     width: 247px;
                     height: 21px;
                 }
        .style92
        {
                     height: 22px;
                     width: 247px;
                 }
        .style96
        {
        }
        .style102
        {
                     height: 20px;
                     width: 14px;
                 }
        .style103
        {
                     height: 17px;
                     width: 15px;
                 }
        .style107
    {
        height: 21px;
        width: 15px;
    }
    .style109
    {
                     height: 24px;
                     width: 15px;
                 }
    .style110
    {
                     height: 24px;
                     width: 14px;
                 }
        .style111
    {
        height: 24px;
        }
    .style112
    {
        height: 10px;
        margin-left: 40px;
        }
    .style113
    {
        height: 10px;
        width: 14px;
    }
    .style114
    {
        height: 10px;
        width: 15px;
    }
    .style115
    {
        height: 10px;
        width: 247px;
    }
        .style116
        {
                     height: 23px;
                     width: 14px;
                 }
        .style117
        {
                     height: 21px;
                     width: 14px;
                 }
        .style118
        {
                     width: 14px;
                 }
        .style120
        {
            height: 23px;
            width: 15px;
        }
        .style121
        {
            width: 247px;
            height: 23px;
        }
        .style126
        {
            height: 103px;
            width: 15px;
        }
        .style127
        {
            width: 247px;
            height: 103px;
        }
        .style128
        {
            height: 103px;
        }
        .style129
        {
            width: 14px;
            height: 103px;
        }
                 .style130
                 {
                     width: 247px;
                 }
                 .style131
                 {
                     height: 142px;
                     width: 15px;
                 }
                 .style132
                 {
                     height: 142px;
                 }
                 .style133
                 {
                     width: 14px;
                     height: 142px;
                 }
                 .style134
                 {
                     width: 15px;
                 }
                 </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%; background-image: url('Image/Bg_Sgt.gif'); background-attachment: fixed;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        
            style="border: 1px solid black; margin-left: 0px; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px; width: 83%;" 
            bgcolor="White" class="style81">
        <tr>
            <td align="center" bgcolor="#8BB900" 
                valign="top" colspan="4" 
                
                
                
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" >
                pendaftaran tpc - rekod jururawat</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style83">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style33">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" 
                class="style102">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 10px">
            <td align="left" bgcolor="#ffffff" valign="top" class="style114">&nbsp;</td>
            <TD valign="top" align="left" bgcolor="white" class="style115">&nbsp;</td>
            <td bgcolor="White" class="style112" style="line-height: 5px">&nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style113">&nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp;
									NAMA</td>
            <td bgcolor="White">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style116">
            </td>
        </tr>
        <tr style="line-height: 21px">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td><td valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp; NO. PASPORT</td>
 <td align="left" bgcolor="white" valign="middle" class="style12">
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                    <ContentTemplate>
                        <asp:TextBox ID="Tx_NoKP" 
    runat="server" CssClass="std" Width="189px" 
                                                     Wrap="False" Enabled="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>       </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<td valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp; WARGANEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel10" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Warga" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" 
    CssClass="std">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; TARIKH LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                            <asp:TextBox ID="Tx_Tkh_Lahir" runat="server" CssClass="std" 
                    Width="95px"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; TEMPAT LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_Tpt_Lahir" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									JANTINA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem Value=""></asp:ListItem>
                    <asp:ListItem Value="1">LELAKI</asp:ListItem>
                    <asp:ListItem Value="2">PEREMPUAN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp; UMUR&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_Umur" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; ALAMAT TETAP</TD>
            <td align="left" bgcolor="white" valign="top" 
                id="Tx_TP_Alamat" class="style33">
                <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp;
									BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style9">
                <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style116">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style87">&nbsp; NEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; ALAMAT SURAT MENYURAT</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
                &nbsp;</td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									NEGERI</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:DropDownList ID="Cb_SM_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103">
            </td>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									E-MEL</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" bgcolor="#ffffff" class="style130">&nbsp;</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                        </td>
								<TD valign="top" bgcolor="#ffffff" class="style130">&nbsp;&nbsp;INSTITUSI LATIHAN</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="500px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                        </td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH TAMAT 
                                    LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; LEMBAGA PENDAFTAR&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_LP" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; NO. PENDAFTARAN LP</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_LP_NoPd" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH PENGESAHAN 
                                    SIJIL LP</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_LP_TkhSah" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;
                        </td>
                        <td bgcolor="#ffffff" class="style130">
                        </td>
                        <td bgcolor="#ffffff">
                            <asp:DropDownList ID="Cb_K" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_I" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_M" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_D" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <br />
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; NO. SIRI BORANG PERMOHONAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_NoSiri" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH PERMOHONAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_TkhMohon" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH RESIT</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_TkhResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; NO. RESIT</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_NoResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style130">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" bgcolor="#ffffff" class="style130">&nbsp; KELAYAKAN IKHTISAS</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2">
                        <table align="left"><tr>
                                <td align="left" width="100%">&nbsp;</td></tr></table>
                            
                                     </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" bgcolor="#ffffff" class="style130">&nbsp;&nbsp;KELAYAKAN AKADEMIK</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2">
                            <table align="left"><tr>
                                <td align="left">
                                    &nbsp;</td></tr></table>
                            
                                     </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
								<TD valign="top" bgcolor="#ffffff" class="style130">&nbsp;&nbsp;PENGALAMAN BEKERJA</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style134">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr id="tr1">
                        <td bgcolor="#ffffff" class="style103">
                            </td>
                        <td bgcolor="#999966" colspan="2" 
                            style="border: 1px solid #999966; color: #FFFFFF; font-weight: bold;">
                                                        &nbsp; Senarai Semak             </tr ><tr>
                        <td  id="tr2" bgcolor="#ffffff" class="style103">
                        </td>
                        <td bgcolor="#F5F5F1" colspan="2" valign="top" 
                            style="border: 1px solid #999966; line-height: normal" align="left"><table align="center"><tr>
                                <td align="left" colspan="1">
                                    <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" RepeatColumns="2" 
                                Width="100%" Height="31px">
                                <asp:ListItem>Salinan Sijil Perkahwinan</asp:ListItem>
                                <asp:ListItem>Salinan Pasport</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Lahir</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Akademik</asp:ListItem>
                                <asp:ListItem>Foto</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Ikhtisas</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Pendaftaran dengan Lembaga Jururawat</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Amalan Kejururawatan</asp:ListItem>
                                <asp:ListItem>Letter of Good Standing</asp:ListItem>
                                <asp:ListItem>Salinan VOR</asp:ListItem>
                                <asp:ListItem>Salinan VOT</asp:ListItem>
                                <asp:ListItem>Transkrip Latihan</asp:ListItem>
                                <asp:ListItem>Surat Perisytiharan dari majikan terkini</asp:ListItem>
                                <asp:ListItem>Bayaran Pendaftaran</asp:ListItem>
                                        <asp:ListItem>Resume</asp:ListItem>
                                        <asp:ListItem>Sijil Pengajar (Kolej)/ Teaching Methodology</asp:ListItem>
                                    </asp:CheckBoxList></td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style103">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style130">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style120">
                            </td>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            </td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
            </td>
                        <td bgcolor="#ffffff" class="style116">
                            </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" class="style103">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style118">
                &nbsp;</td>
        </tr>
    </table></td></tr></table>
    
    </br></br>
    </div>

</asp:Content>
