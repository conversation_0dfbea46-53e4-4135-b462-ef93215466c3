<%@ Page Language="VB" AutoEventWireup="false" CodeFile="TestOtpIntegration.aspx.vb" Inherits="TestOtpIntegration" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Test OTP Integration - SPMJ</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        input[type="text"] { padding: 5px; margin: 5px; width: 200px; }
        .log { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>SPMJ OTP Integration Test Page</h1>
            <p>This page helps test the communication between the .NET 3.5 application and the .NET 9 email microservice.</p>
            
            <!-- Microservice Health Check -->
            <div class="test-section">
                <h3>1. Microservice Health Check</h3>
                <asp:Button ID="btnHealthCheck" runat="server" Text="Check Health" CssClass="btn btn-primary" />
                <asp:Label ID="lblHealthStatus" runat="server" />
            </div>
            
            <!-- User Email Test -->
            <div class="test-section">
                <h3>2. User Email Lookup</h3>
                <label>User ID:</label>
                <asp:TextBox ID="txtUserId" runat="server" placeholder="Enter user ID" />
                <asp:Button ID="btnGetEmail" runat="server" Text="Get Email" CssClass="btn btn-primary" />
                <br />
                <asp:Label ID="lblEmailResult" runat="server" />
            </div>
            
            <!-- OTP Generation Test -->
            <div class="test-section">
                <h3>3. OTP Generation Test</h3>
                <label>User ID:</label>
                <asp:TextBox ID="txtOtpUserId" runat="server" placeholder="Enter user ID" />
                <label>Email:</label>
                <asp:TextBox ID="txtOtpEmail" runat="server" placeholder="Enter email" />
                <br />
                <asp:Button ID="btnGenerateOtp" runat="server" Text="Generate OTP" CssClass="btn btn-success" />
                <asp:Label ID="lblOtpResult" runat="server" />
            </div>
            
            <!-- OTP Validation Test -->
            <div class="test-section">
                <h3>4. OTP Validation Test</h3>
                <label>User ID:</label>
                <asp:TextBox ID="txtValidateUserId" runat="server" placeholder="Enter user ID" />
                <label>OTP Code:</label>
                <asp:TextBox ID="txtOtpCode" runat="server" placeholder="Enter 6-digit OTP" />
                <br />
                <asp:Button ID="btnValidateOtp" runat="server" Text="Validate OTP" CssClass="btn btn-warning" />
                <asp:Label ID="lblValidateResult" runat="server" />
            </div>
            
            <!-- Configuration Check -->
            <div class="test-section">
                <h3>5. Configuration Check</h3>
                <asp:Button ID="btnCheckConfig" runat="server" Text="Check Configuration" CssClass="btn btn-primary" />
                <asp:Label ID="lblConfigResult" runat="server" />
            </div>
            
            <!-- Debug Log -->
            <div class="test-section">
                <h3>Debug Log</h3>
                <asp:Button ID="btnClearLog" runat="server" Text="Clear Log" CssClass="btn btn-warning" />
                <div id="debugLog" class="log" runat="server"></div>
            </div>
        </div>
    </form>
</body>
</html>
