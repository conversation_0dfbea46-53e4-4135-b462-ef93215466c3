# Test P0 Login OTP Integration with Microservice
# This script tests the communication between p0_Login.aspx and the email microservice

Write-Host "=== SPMJ P0 Login OTP Integration Test ===" -ForegroundColor Green
Write-Host ""

# Test 1: Check if microservice is running
Write-Host "1. Testing Email Microservice Health..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:5000/health" -Method GET -TimeoutSec 10
    Write-Host "✅ Microservice is running" -ForegroundColor Green
    Write-Host "   Response: $($healthResponse | ConvertTo-Json -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Microservice is not running or not accessible" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please start the microservice first:" -ForegroundColor Yellow
    Write-Host "   cd 'SPMJ.EmailService'" -ForegroundColor Gray
    Write-Host "   dotnet run" -ForegroundColor Gray
    exit 1
}

Write-Host ""

# Test 2: Test OTP Generation API
Write-Host "2. Testing OTP Generation API..." -ForegroundColor Yellow
$otpRequest = @{
    UserId = "test123"
    Email = "<EMAIL>"
    Purpose = "LOGIN"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
    "X-API-Key" = "SPMJ-EmailService-2024-SecureKey-MOH-Malaysia"
}

try {
    $otpResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/otp/generate" -Method POST -Body $otpRequest -Headers $headers -TimeoutSec 10
    Write-Host "✅ OTP Generation API is working" -ForegroundColor Green
    Write-Host "   Response: $($otpResponse | ConvertTo-Json -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "❌ OTP Generation API failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "   Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""

# Test 3: Test OTP Validation API
Write-Host "3. Testing OTP Validation API..." -ForegroundColor Yellow
$validateRequest = @{
    UserId = "test123"
    OtpCode = "123456"
    Purpose = "LOGIN"
} | ConvertTo-Json

try {
    $validateResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/otp/validate" -Method POST -Body $validateRequest -Headers $headers -TimeoutSec 10
    Write-Host "✅ OTP Validation API is working" -ForegroundColor Green
    Write-Host "   Response: $($validateResponse | ConvertTo-Json -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "❌ OTP Validation API failed" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "   Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: Check Web.config settings
Write-Host "4. Checking Web.config settings..." -ForegroundColor Yellow
$webConfigPath = "SPMJ -PDSA\SPMJ\Web.config"
if (Test-Path $webConfigPath) {
    [xml]$webConfig = Get-Content $webConfigPath
    $emailServiceUrl = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "EmailServiceUrl" } | Select-Object -ExpandProperty value
    $otpEnabled = $webConfig.configuration.appSettings.add | Where-Object { $_.key -eq "OtpEnabled" } | Select-Object -ExpandProperty value
    
    Write-Host "   EmailServiceUrl: $emailServiceUrl" -ForegroundColor Gray
    Write-Host "   OtpEnabled: $otpEnabled" -ForegroundColor Gray
    
    if ($emailServiceUrl -eq "http://localhost:5000" -and $otpEnabled -eq "true") {
        Write-Host "✅ Web.config settings are correct" -ForegroundColor Green
    } else {
        Write-Host "❌ Web.config settings need adjustment" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Web.config file not found at $webConfigPath" -ForegroundColor Red
}

Write-Host ""

# Test 5: Check if database has users with email addresses
Write-Host "5. Database connectivity test..." -ForegroundColor Yellow
Write-Host "   Note: This requires the database to be accessible" -ForegroundColor Gray
Write-Host "   Check that users in pn_pengguna table have valid email addresses" -ForegroundColor Gray

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Green
Write-Host "If all tests pass, the p0_Login.aspx OTP integration should work." -ForegroundColor White
Write-Host ""
Write-Host "To test the full flow:" -ForegroundColor Yellow
Write-Host "1. Ensure the microservice is running (dotnet run in SPMJ.EmailService)" -ForegroundColor Gray
Write-Host "2. Ensure a user in the database has a valid email address" -ForegroundColor Gray
Write-Host "3. Try logging in with that user via p0_Login.aspx" -ForegroundColor Gray
Write-Host "4. Check the browser developer console and application debug output" -ForegroundColor Gray
Write-Host ""
