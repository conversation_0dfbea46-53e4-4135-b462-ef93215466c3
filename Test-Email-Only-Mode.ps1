# Test Email-Only Mode
Write-Host "=== Email-Only Mode Test ===" -ForegroundColor Green

# Test microservice
Write-Host "Testing microservice..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:5000/health" -TimeoutSec 5
    Write-Host "✅ Microservice: $($health.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ Microservice not responding" -ForegroundColor Red
    exit 1
}

# Test email endpoint
Write-Host "Testing email endpoint..." -ForegroundColor Yellow
$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
}

$testRequest = @{
    UserId = '820228115693'
    Email = '<EMAIL>'
    UserName = 'ONG SU HANG'
    TempPassword = 'TestPass123!'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/password/reset/send' -Method POST -Body $testRequest -Headers $headers -TimeoutSec 10
    
    if ($response.success) {
        Write-Host "✅ Email endpoint working" -ForegroundColor Green
        Write-Host "   Message: $($response.message)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Email endpoint failed: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Email endpoint error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Status ===" -ForegroundColor Green
Write-Host "✅ Microservice: Running" -ForegroundColor Green
Write-Host "✅ Email integration: Active" -ForegroundColor Green
Write-Host "✅ Fallback mode: Disabled" -ForegroundColor Green
Write-Host ""
Write-Host "Password recovery will ONLY send emails!" -ForegroundColor Cyan
Write-Host ""
Write-Host "Test at p0_Login.aspx with User ID: 820228115693" -ForegroundColor Yellow
