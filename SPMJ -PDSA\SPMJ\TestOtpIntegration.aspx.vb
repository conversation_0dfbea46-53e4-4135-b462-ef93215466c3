Imports System.Data.OleDb
Imports System.Configuration

Partial Public Class TestOtpIntegration
    Inherits System.Web.UI.Page

    Private emailServiceClient As EmailServiceClient
    Private debugMessages As New List(Of String)

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ' Initialize email service client
            Try
                Dim serviceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
                If String.IsNullOrEmpty(serviceUrl) Then serviceUrl = "http://localhost:5000"
                emailServiceClient = New EmailServiceClient(serviceUrl)
                AddDebugMessage("Email service client initialized with URL: " & serviceUrl)
            Catch ex As Exception
                AddDebugMessage("Failed to initialize email service client: " & ex.Message)
            End Try
        End If
    End Sub

    Protected Sub btnHealthCheck_Click(sender As Object, e As EventArgs) Handles btnHealthCheck.Click
        Try
            If emailServiceClient Is Nothing Then
                lblHealthStatus.Text = "<div class='error'>Email service client not initialized</div>"
                AddDebugMessage("Health check failed: Client not initialized")
                Return
            End If

            Dim isHealthy As Boolean = emailServiceClient.CheckHealth()
            If isHealthy Then
                lblHealthStatus.Text = "<div class='success'>✅ Microservice is healthy and responding</div>"
                AddDebugMessage("Health check successful")
            Else
                lblHealthStatus.Text = "<div class='error'>❌ Microservice is not responding</div>"
                AddDebugMessage("Health check failed: No response")
            End If

        Catch ex As Exception
            lblHealthStatus.Text = "<div class='error'>❌ Health check error: " & ex.Message & "</div>"
            AddDebugMessage("Health check exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnGetEmail_Click(sender As Object, e As EventArgs) Handles btnGetEmail.Click
        Try
            If String.IsNullOrEmpty(txtUserId.Text.Trim()) Then
                lblEmailResult.Text = "<div class='error'>Please enter a user ID</div>"
                Return
            End If

            Dim email As String = GetUserEmail(txtUserId.Text.Trim())
            If Not String.IsNullOrEmpty(email) Then
                lblEmailResult.Text = "<div class='success'>✅ Email found: " & email & "</div>"
                AddDebugMessage("Email lookup successful for user " & txtUserId.Text & ": " & email)
                
                ' Auto-populate OTP fields
                txtOtpUserId.Text = txtUserId.Text
                txtOtpEmail.Text = email
                txtValidateUserId.Text = txtUserId.Text
            Else
                lblEmailResult.Text = "<div class='error'>❌ No email found for user: " & txtUserId.Text & "</div>"
                AddDebugMessage("Email lookup failed for user " & txtUserId.Text)
            End If

        Catch ex As Exception
            lblEmailResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("Email lookup exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnGenerateOtp_Click(sender As Object, e As EventArgs) Handles btnGenerateOtp.Click
        Try
            If String.IsNullOrEmpty(txtOtpUserId.Text.Trim()) OrElse String.IsNullOrEmpty(txtOtpEmail.Text.Trim()) Then
                lblOtpResult.Text = "<div class='error'>Please enter both User ID and Email</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                lblOtpResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            AddDebugMessage("Attempting to generate OTP for user: " & txtOtpUserId.Text & ", email: " & txtOtpEmail.Text)
            
            Dim response = emailServiceClient.GenerateOTP(txtOtpUserId.Text.Trim(), txtOtpEmail.Text.Trim(), "LOGIN")
            
            If response.Success Then
                lblOtpResult.Text = "<div class='success'>✅ OTP generated successfully: " & response.Message & "</div>"
                AddDebugMessage("OTP generation successful: " & response.Message)
            Else
                lblOtpResult.Text = "<div class='error'>❌ OTP generation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP generation failed: " & response.Message)
            End If

        Catch ex As Exception
            lblOtpResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("OTP generation exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnValidateOtp_Click(sender As Object, e As EventArgs) Handles btnValidateOtp.Click
        Try
            If String.IsNullOrEmpty(txtValidateUserId.Text.Trim()) OrElse String.IsNullOrEmpty(txtOtpCode.Text.Trim()) Then
                lblValidateResult.Text = "<div class='error'>Please enter both User ID and OTP Code</div>"
                Return
            End If

            If emailServiceClient Is Nothing Then
                lblValidateResult.Text = "<div class='error'>Email service client not initialized</div>"
                Return
            End If

            AddDebugMessage("Attempting to validate OTP for user: " & txtValidateUserId.Text & ", code: " & txtOtpCode.Text)
            
            Dim response = emailServiceClient.ValidateOTP(txtValidateUserId.Text.Trim(), txtOtpCode.Text.Trim(), "LOGIN")
            
            If response.Success Then
                lblValidateResult.Text = "<div class='success'>✅ OTP validation successful: " & response.Message & "</div>"
                AddDebugMessage("OTP validation successful: " & response.Message)
            Else
                lblValidateResult.Text = "<div class='error'>❌ OTP validation failed: " & response.Message & "</div>"
                AddDebugMessage("OTP validation failed: " & response.Message)
            End If

        Catch ex As Exception
            lblValidateResult.Text = "<div class='error'>❌ Error: " & ex.Message & "</div>"
            AddDebugMessage("OTP validation exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnCheckConfig_Click(sender As Object, e As EventArgs) Handles btnCheckConfig.Click
        Try
            Dim configInfo As New System.Text.StringBuilder()
            
            ' Check configuration settings
            Dim emailServiceUrl As String = ConfigurationManager.AppSettings("EmailServiceUrl")
            Dim otpEnabled As String = ConfigurationManager.AppSettings("OtpEnabled")
            Dim emailServiceEnabled As String = ConfigurationManager.AppSettings("EmailServiceEnabled")
            
            configInfo.AppendLine("EmailServiceUrl: " & If(emailServiceUrl, "NOT SET"))
            configInfo.AppendLine("OtpEnabled: " & If(otpEnabled, "NOT SET"))
            configInfo.AppendLine("EmailServiceEnabled: " & If(emailServiceEnabled, "NOT SET"))
            
            ' Check database connection
            Try
                Dim cn As New OleDbConnection(SPMJ_Mod.ServerId)
                cn.Open()
                configInfo.AppendLine("Database Connection: ✅ SUCCESS")
                cn.Close()
            Catch ex As Exception
                configInfo.AppendLine("Database Connection: ❌ FAILED - " & ex.Message)
            End Try
            
            lblConfigResult.Text = "<div class='info'><pre>" & configInfo.ToString() & "</pre></div>"
            AddDebugMessage("Configuration check completed")

        Catch ex As Exception
            lblConfigResult.Text = "<div class='error'>❌ Configuration check error: " & ex.Message & "</div>"
            AddDebugMessage("Configuration check exception: " & ex.Message)
        End Try
        
        UpdateDebugLog()
    End Sub

    Protected Sub btnClearLog_Click(sender As Object, e As EventArgs) Handles btnClearLog.Click
        debugMessages.Clear()
        UpdateDebugLog()
    End Sub

    Private Sub AddDebugMessage(message As String)
        Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
        debugMessages.Add("[" & timestamp & "] " & message)
    End Sub

    Private Sub UpdateDebugLog()
        debugLog.InnerHtml = String.Join(vbCrLf, debugMessages.ToArray())
    End Sub

    Private Function GetUserEmail(userId As String) As String
        Try
            Dim email As String = ""
            Dim cn As New OleDbConnection
            Dim cmd As New OleDbCommand
            
            cn.ConnectionString = SPMJ_Mod.ServerId
            cn.Open()
            cmd.Connection = cn
            cmd.CommandText = "SELECT email FROM pn_pengguna WHERE id_pg = ? AND status = 1"
            cmd.Parameters.AddWithValue("@id_pg", userId)
            
            Dim result = cmd.ExecuteScalar()
            If result IsNot Nothing AndAlso Not IsDBNull(result) Then
                email = result.ToString().Trim()
            End If
            
            cn.Close()
            Return email
        Catch ex As Exception
            AddDebugMessage("GetUserEmail exception: " & ex.Message)
            Return ""
        End Try
    End Function

End Class
