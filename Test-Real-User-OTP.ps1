# Test OTP with real user from database
Write-Host "=== Testing OTP with Real User ===" -ForegroundColor Green

$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
}

$userId = '820228115693'
$email = '<EMAIL>'

# Test OTP Generation
Write-Host "1. Testing OTP Generation..." -ForegroundColor Yellow
$body = @{
    UserId = $userId
    Email = $email
    Purpose = 'LOGIN'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/otp/generate' -Method POST -Body $body -Headers $headers
    Write-Host "✅ OTP Generation Success: $($response.success)" -ForegroundColor Green
    Write-Host "   Message: $($response.message)" -ForegroundColor Gray
    if ($response.otpCode) {
        Write-Host "   OTP Code: $($response.otpCode)" -ForegroundColor Cyan
        $global:generatedOtp = $response.otpCode
    }
} catch {
    Write-Host "❌ OTP Generation Failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $stream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($stream)
        $responseBody = $reader.ReadToEnd()
        Write-Host "   Response: $responseBody" -ForegroundColor Red
        $reader.Close()
        $stream.Close()
    }
}

Write-Host ""

# Test OTP Validation (if we got an OTP)
if ($global:generatedOtp) {
    Write-Host "2. Testing OTP Validation..." -ForegroundColor Yellow
    $validateBody = @{
        UserId = $userId
        OtpCode = $global:generatedOtp
        Purpose = 'LOGIN'
    } | ConvertTo-Json

    try {
        $validateResponse = Invoke-RestMethod -Uri 'http://localhost:5000/api/otp/validate' -Method POST -Body $validateBody -Headers $headers
        Write-Host "✅ OTP Validation Success: $($validateResponse.success)" -ForegroundColor Green
        Write-Host "   Message: $($validateResponse.message)" -ForegroundColor Gray
        Write-Host "   Data: $($validateResponse.data)" -ForegroundColor Gray
    } catch {
        Write-Host "❌ OTP Validation Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "2. Skipping OTP Validation (no OTP generated)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
Write-Host "User ID: $userId" -ForegroundColor Gray
Write-Host "Email: $email" -ForegroundColor Gray
