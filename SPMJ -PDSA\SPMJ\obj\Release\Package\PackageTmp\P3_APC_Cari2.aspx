﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_APC_Cari2.aspx.vb" Inherits="SPMJ.P3_APC_Cari2" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style3
        {
            height: 23px;
        }
        .style5
        {
            width: 31px;
        }
        .style6
        {
        }
        .style7
        {
        }
        .style8
        {
            width: 31px;
            height: 35px;
        }
        .style9
        {
            height: 35px;
        }
        .style16
        {
            width: 31px;
            height: 24px;
        }
        .style17
        {
            height: 24px;
        }
        .style18
        {
            width: 31px;
            height: 36px;
        }
        .style19
        {
            height: 36px;
        }
        .style20
        {
            width: 31px;
            height: 23px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps; background-attachment: fixed;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr style="width: 20px">
            <td width="20" rowspan="3">&nbsp;</td>
            <td width="600" class="style3"></td>
            <td width="20" rowspan="3">&nbsp;</td>
            <td width="10" rowspan="3" 
                             
                style="border-left-style: solid; border-left-width: 1px; border-color: #9A9A9A" 
                bgcolor="White">
                &nbsp;</td>
            <td bgcolor="White" class="style3">
                
                         </td>
            <td bgcolor="White" rowspan="3" style="width: 20px">&nbsp;</td>
        </tr>
        <tr>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td bgcolor="White" align="right">
                
                &nbsp;</td>
        </tr>
        <tr>
            <td width="600" valign="top">
                <table style="width:100%;" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="style16">
                            </td>
                        <td class="style17" colspan="2" 
                            
                            style="border-color: #7EA851; border-width: 1px; font-family: Arial; font-size: 8pt; font-variant: small-caps; background-color: #7EA851; color: #FFFFFF; border-right-style: solid; border-left-style: solid;">
                            &nbsp;&nbsp;
                            &nbsp;Pembaharuan Apc - Proses Apc&nbsp;</td>
                        <td class="style17">
                            </td>
                    </tr>
                    <tr>
                        <td class="style18">
                            </td>
                        <td class="style19" colspan="2" 
                            style="border-right-style: solid; border-left-style: solid; border-color: #7EA851; border-width: 1px">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">JENIS APC</asp:TextBox>
                <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" Width="192px" CssClass="std">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">BIDAN</asp:ListItem>
                </asp:DropDownList>
                        </td>
                        <td class="style19">
                            </td>
                    </tr>
                    <tr>
                        <td class="style5">
                            &nbsp;</td>
                        <td class="style7" colspan="2" 
                            style="border-right-style: solid; border-left-style: solid; border-color: #7EA851; border-width: 1px">
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="190px" 
                    Wrap="False"></asp:TextBox>
                        </td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style5">
                            &nbsp;</td>
                        <td class="style7" colspan="2" 
                            style="border-right-style: solid; border-left-style: solid; border-color: #7EA851; border-width: 1px">
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NO. KAD PENGENALAN</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False"></asp:TextBox>
                                                 </td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style20">
                            </td>
                        <td class="style3" colspan="2" 
                            style="border-right-style: solid; border-left-style: solid; border-color: #7EA851; border-width: 1px">
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NO. PENDAFTARAN</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoPd" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False"></asp:TextBox>
                                                 </td>
                        <td class="style3">
                            </td>
                    </tr>
                    <tr>
                        <td class="style8">
                            </td>
                        <td class="style9" colspan="2" 
                            style="border-right-style: solid; border-left-style: solid; border-color: #7EA851; border-width: 1px; border-bottom-style: solid;">
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
                            <asp:Button ID="cmd_Proses" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="PROSES APC" Width="120px" 
                    Visible="False" />
                            </td>
                        <td class="style9">
                            </td>
                    </tr>
                    <tr>
                        <td class="style5">
                            &nbsp;</td>
                        <td class="style7">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style5">
                            &nbsp;</td>
                        <td class="style6" colspan="2">
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField ShowHeader="False">
                                    <ItemTemplate>
                                        <asp:Button ID="Button3" runat="server" CausesValidation="False" 
                                            CommandName="Pilih" Font-Bold="True" Font-Names="Lucida Console" Height="20px" 
                                            Text="+" onclick="Button3_Click" Width="20px" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField ShowHeader="False">
                                    <ItemTemplate>
                                        <asp:Button ID="Button4" runat="server" CausesValidation="False" 
                                            CommandName="Pinda" Font-Bold="False" Height="20px" Text="..." 
                                            Width="20px" onclick="Button4_Click" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="False" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                        </td>
                        <td>
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td class="style5">
                            &nbsp;</td>
                        <td class="style6" colspan="2">
                            &nbsp;</td>
                        <td>
                            &nbsp;</td>
                    </tr>
                </table>
                         </td>
            <td bgcolor="White" align="left" valign="top">
                
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <asp:GridView ID="Gd_Pilih" runat="server" BorderColor="#719548" 
                                CellPadding="1" Font-Names="Arial" 
    Font-Size="8pt" ForeColor="Black" 
                                GridLines="Horizontal" HorizontalAlign="Left" 
    Width="100%">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField ShowHeader="False">
                                    <ItemTemplate>
                                        <asp:Button ID="Button2" runat="server" CausesValidation="False" 
                                                CommandName="Select" Font-Bold="True" Font-Names="Lucida Console" 
                                                Font-Size="8pt" ForeColor="#CC0000" Height="20px" Text="X" 
                                            Width="20px" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="False" />
                            <HeaderStyle BackColor="#F19203" CssClass="menu_small" Font-Bold="True" 
                                    ForeColor="White" Height="21px" HorizontalAlign="Left" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                    </ContentTemplate>
                </asp:UpdatePanel>
                
            </td>
        </tr>
            
    
    </table>
    
    
    </div></asp:Content>
