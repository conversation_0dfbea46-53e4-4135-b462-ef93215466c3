Imports System.Data.OleDb
Imports System.Text

Partial Public Class DebugPasswordDatabase
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ' Pre-fill with current session user if available
            If Session("Id_PG") IsNot Nothing Then
                txt_UserId.Text = Session("Id_PG").ToString()
            End If
        End If
    End Sub

    Protected Sub btn_Check_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btn_Check.Click
        If String.IsNullOrEmpty(txt_UserId.Text.Trim()) Then
            lit_Results.Text = "<div class='error'>Please enter a User ID</div>"
            pnl_Results.Visible = True
            Return
        End If

        CheckUserPasswordData(txt_UserId.Text.Trim())
    End Sub

    Protected Sub btn_TestPassword_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btn_TestPassword.Click
        If String.IsNullOrEmpty(txt_UserId.Text.Trim()) Or String.IsNullOrEmpty(txt_TestPassword.Text) Then
            lit_TestResults.Text = "<div class='error'>Please enter both User ID and Password</div>"
            Return
        End If

        TestPasswordVerification(txt_UserId.Text.Trim(), txt_TestPassword.Text)
    End Sub

    Private Sub CheckUserPasswordData(userId As String)
        Dim connection As OleDbConnection = Nothing
        Dim results As New StringBuilder()

        Try
            connection = New OleDbConnection(SPMJ_Mod.ServerId)
            connection.Open()

            results.AppendLine("<div class='success'>✅ Database connection successful</div>")
            results.AppendLine("<h4>Connection String:</h4>")
            results.AppendLine("<div class='debug-info'>" & SPMJ_Mod.ServerId & "</div>")

            ' First, check if user exists
            Using command As New OleDbCommand("SELECT COUNT(*) FROM pn_pengguna WHERE id_pg = ?", connection)
                command.Parameters.AddWithValue("@id_pg", userId)
                Dim userCount As Integer = Convert.ToInt32(command.ExecuteScalar())
                
                If userCount = 0 Then
                    results.AppendLine("<div class='error'>❌ User '" & userId & "' not found in database</div>")
                    lit_Results.Text = results.ToString()
                    pnl_Results.Visible = True
                    Return
                End If
            End Using

            ' Get all available columns
            Using command As New OleDbCommand("SELECT * FROM pn_pengguna WHERE id_pg = ?", connection)
                command.Parameters.AddWithValue("@id_pg", userId)
                
                Using reader As OleDbDataReader = command.ExecuteReader()
                    If reader.Read() Then
                        results.AppendLine("<h4>Available Columns:</h4>")
                        results.AppendLine("<table>")
                        results.AppendLine("<tr><th>Column Name</th><th>Value</th><th>Type</th></tr>")
                        
                        For i As Integer = 0 To reader.FieldCount - 1
                            Dim columnName As String = reader.GetName(i)
                            Dim value As Object = reader(i)
                            Dim valueStr As String = If(value Is DBNull.Value, "[NULL]", value.ToString())
                            Dim dataType As String = reader.GetFieldType(i).Name
                            
                            results.AppendLine("<tr>")
                            results.AppendLine("<td>" & columnName & "</td>")
                            results.AppendLine("<td>" & valueStr & "</td>")
                            results.AppendLine("<td>" & dataType & "</td>")
                            results.AppendLine("</tr>")
                        Next
                        
                        results.AppendLine("</table>")
                        
                        ' Specific password-related analysis
                        results.AppendLine("<h4>Password Analysis:</h4>")
                        
                        Dim pwd As String = GetSafeValue(reader, "pwd")
                        Dim salt As String = GetSafeValue(reader, "salt")
                        Dim encrypted As String = GetSafeValue(reader, "pwd_encrypted")
                        
                        results.AppendLine("<div class='debug-info'>")
                        results.AppendLine("<strong>Password:</strong> '" & pwd & "' (Length: " & pwd.Length & ")<br/>")
                        results.AppendLine("<strong>Salt:</strong> '" & salt & "' (Length: " & salt.Length & ")<br/>")
                        results.AppendLine("<strong>Encrypted Flag:</strong> " & encrypted & "<br/>")
                        results.AppendLine("</div>")
                    End If
                End Using
            End Using

        Catch ex As Exception
            results.AppendLine("<div class='error'>❌ Database error: " & ex.Message & "</div>")
        Finally
            If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                connection.Close()
            End If
        End Try

        lit_Results.Text = results.ToString()
        pnl_Results.Visible = True
    End Sub

    Private Sub TestPasswordVerification(userId As String, password As String)
        Dim results As New StringBuilder()
        
        Try
            results.AppendLine("<h4>Password Verification Test:</h4>")
            results.AppendLine("<div class='debug-info'>")
            results.AppendLine("<strong>User ID:</strong> " & userId & "<br/>")
            results.AppendLine("<strong>Password Length:</strong> " & password.Length & "<br/>")
            results.AppendLine("</div>")
            
            ' Test using the same logic as PN_Pwd.aspx
            Dim connection As OleDbConnection = Nothing
            
            Try
                connection = New OleDbConnection(SPMJ_Mod.ServerId)
                connection.Open()
                
                Using command As New OleDbCommand("SELECT pwd, salt, pwd_encrypted FROM pn_pengguna WHERE id_pg = ?", connection)
                    command.Parameters.AddWithValue("@id_pg", userId)
                    
                    Using reader As OleDbDataReader = command.ExecuteReader()
                        If reader.Read() Then
                            Dim storedPassword As String = GetSafeValue(reader, "pwd")
                            Dim salt As String = GetSafeValue(reader, "salt")
                            Dim isEncrypted As Boolean = GetSafeBoolValue(reader, "pwd_encrypted")
                            
                            results.AppendLine("<div class='debug-info'>")
                            results.AppendLine("<strong>Stored Password:</strong> '" & storedPassword & "'<br/>")
                            results.AppendLine("<strong>Salt:</strong> '" & salt & "'<br/>")
                            results.AppendLine("<strong>Is Encrypted:</strong> " & isEncrypted & "<br/>")
                            results.AppendLine("</div>")
                            
                            ' Test verification methods
                            If isEncrypted AndAlso Not String.IsNullOrEmpty(salt) Then
                                results.AppendLine("<div class='warning'>Testing encrypted password verification...</div>")
                                
                                ' Test standard verification
                                Try
                                    Dim result1 As Boolean = PasswordHelper.VerifyPassword(password, storedPassword, salt)
                                    results.AppendLine("<div class='" & If(result1, "success", "error") & "'>Standard verification: " & result1 & "</div>")
                                Catch ex As Exception
                                    results.AppendLine("<div class='error'>Standard verification error: " & ex.Message & "</div>")
                                End Try
                                
                                ' Test workaround verification
                                Try
                                    Dim result2 As Boolean = PasswordHelper.VerifyPasswordWorkaround(password, storedPassword, salt)
                                    results.AppendLine("<div class='" & If(result2, "success", "error") & "'>Workaround verification: " & result2 & "</div>")
                                Catch ex As Exception
                                    results.AppendLine("<div class='error'>Workaround verification error: " & ex.Message & "</div>")
                                End Try
                            Else
                                results.AppendLine("<div class='warning'>Testing plain text verification...</div>")
                                
                                Dim result As Boolean = storedPassword.Equals(password)
                                results.AppendLine("<div class='" & If(result, "success", "error") & "'>Plain text verification: " & result & "</div>")
                                
                                If Not result Then
                                    results.AppendLine("<div class='debug-info'>")
                                    results.AppendLine("Exact comparison: '" & storedPassword & "' = '" & password & "' ? " & result & "<br/>")
                                    results.AppendLine("Trimmed comparison: '" & storedPassword.Trim() & "' = '" & password.Trim() & "' ? " & storedPassword.Trim().Equals(password.Trim()) & "<br/>")
                                    results.AppendLine("Case-insensitive: '" & storedPassword.ToLower() & "' = '" & password.ToLower() & "' ? " & storedPassword.ToLower().Equals(password.ToLower()) & "<br/>")
                                    results.AppendLine("</div>")
                                End If
                            End If
                        Else
                            results.AppendLine("<div class='error'>User not found in database</div>")
                        End If
                    End Using
                End Using
                
            Finally
                If connection IsNot Nothing AndAlso connection.State = ConnectionState.Open Then
                    connection.Close()
                End If
            End Try
            
        Catch ex As Exception
            results.AppendLine("<div class='error'>Test error: " & ex.Message & "</div>")
        End Try
        
        lit_TestResults.Text = results.ToString()
    End Sub

    Private Function GetSafeValue(reader As OleDbDataReader, columnName As String) As String
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return ""
            End If
            Return reader(columnName).ToString()
        Catch
            Return "[COLUMN NOT FOUND]"
        End Try
    End Function

    Private Function GetSafeBoolValue(reader As OleDbDataReader, columnName As String) As Boolean
        Try
            If reader.IsDBNull(reader.GetOrdinal(columnName)) Then
                Return False
            End If
            Dim value As Object = reader(columnName)
            Return Convert.ToBoolean(value)
        Catch
            Return False
        End Try
    End Function

End Class
