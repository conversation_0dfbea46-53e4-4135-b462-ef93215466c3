# Complete Microservice Email Integration Setup
Write-Host "=== SPMJ Email Microservice Integration Setup ===" -ForegroundColor Green
Write-Host ""

# Step 1: Aggressive cleanup
Write-Host "Step 1: Cleaning up all processes and ports..." -ForegroundColor Yellow
taskkill /F /IM dotnet.exe 2>$null
taskkill /F /IM SPMJ.EmailService.exe 2>$null

# Clear ports 5000-5005
for ($port = 5000; $port -le 5005; $port++) {
    $processes = netstat -ano | Select-String ":$port.*LISTENING"
    if ($processes) {
        foreach ($process in $processes) {
            $pid = ($process.Line -split '\s+')[-1]
            if ($pid -match '^\d+$') {
                taskkill /F /PID $pid 2>$null
                Write-Host "Freed port $port (PID: $pid)" -ForegroundColor Gray
            }
        }
    }
}

Start-Sleep -Seconds 3
Write-Host "✅ Cleanup complete" -ForegroundColor Green

# Step 2: Find available port
Write-Host ""
Write-Host "Step 2: Finding available port..." -ForegroundColor Yellow
$availablePort = $null
for ($testPort = 5000; $testPort -le 5010; $testPort++) {
    $portInUse = netstat -ano | Select-String ":$testPort.*LISTENING"
    if (-not $portInUse) {
        $availablePort = $testPort
        Write-Host "✅ Found available port: $availablePort" -ForegroundColor Green
        break
    }
}

if (-not $availablePort) {
    Write-Host "❌ No available ports found" -ForegroundColor Red
    exit 1
}

# Step 3: Update Web.config
Write-Host ""
Write-Host "Step 3: Updating Web.config for port $availablePort..." -ForegroundColor Yellow
$webConfigPath = "SPMJ -PDSA\SPMJ\Web.config"
if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    $webConfig = $webConfig -replace 'http://localhost:500\d', "http://localhost:$availablePort"
    Set-Content $webConfigPath $webConfig
    Write-Host "✅ Updated Web.config" -ForegroundColor Green
} else {
    Write-Host "❌ Web.config not found" -ForegroundColor Red
}

# Step 4: Remove fallback mechanism to force email integration
Write-Host ""
Write-Host "Step 4: Configuring for email-only mode..." -ForegroundColor Yellow

# Update p0_Login.aspx.vb to remove fallback and force email integration
$loginVbPath = "SPMJ -PDSA\SPMJ\p0_Login.aspx.vb"
if (Test-Path $loginVbPath) {
    $loginVb = Get-Content $loginVbPath -Raw
    
    # Replace the fallback mechanism with strict email requirement
    $loginVb = $loginVb -replace 'If emailServiceAvailable Then.*?Else.*?End If', @'
            ' Require email service to be available - no fallback
            If Not emailClient.CheckHealth() Then
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Sistem email tidak tersedia pada masa ini. Sila cuba lagi kemudian.</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email service not available - no fallback")
                Return
            End If

            System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email service health check passed")

            ' Send password reset email via microservice
            Dim response = emailClient.SendPasswordResetEmail(userId, userEmail, userName, tempPassword)

            If response.Success Then
                lbl_RecoveryMessage.Text = "<span style='color: green;'>Kata laluan sementara telah dihantar ke email anda: " & MaskEmail(userEmail) & "</span>"
                txt_RecoveryUserId.Text = ""
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email sent successfully via microservice")
            Else
                lbl_RecoveryMessage.Text = "<span style='color: red;'>Gagal menghantar email: " & response.Message & "</span>"
                System.Diagnostics.Debug.WriteLine("PASSWORD RECOVERY: Email sending failed: " & response.Message)
            End If
'@
    
    Set-Content $loginVbPath $loginVb
    Write-Host "✅ Configured for email-only mode" -ForegroundColor Green
} else {
    Write-Host "❌ p0_Login.aspx.vb not found" -ForegroundColor Red
}

# Step 5: Build and start microservice
Write-Host ""
Write-Host "Step 5: Building and starting microservice..." -ForegroundColor Yellow
try {
    Set-Location "SPMJ.EmailService"
    
    # Clean and build
    & dotnet clean | Out-Null
    $buildResult = & dotnet build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        Set-Location ".."
        exit 1
    }
    
    Write-Host "✅ Build successful" -ForegroundColor Green
    
    # Start microservice
    Write-Host "Starting microservice on port $availablePort..." -ForegroundColor Gray
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run", "--urls", "http://localhost:$availablePort" -PassThru -WindowStyle Hidden
    Write-Host "✅ Microservice started with PID: $($process.Id)" -ForegroundColor Green
    
    Set-Location ".."
    
    # Wait for service to be ready
    Write-Host "Waiting for microservice to initialize..." -ForegroundColor Gray
    $maxAttempts = 20
    $attempt = 0
    $serviceReady = $false
    
    while ($attempt -lt $maxAttempts -and -not $serviceReady) {
        Start-Sleep -Seconds 2
        $attempt++
        
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:$availablePort/health" -TimeoutSec 3
            if ($response.status -eq "healthy") {
                $serviceReady = $true
                Write-Host "✅ Microservice is healthy and responding" -ForegroundColor Green
            }
        } catch {
            Write-Host "⏳ Attempt $attempt/$maxAttempts - waiting..." -ForegroundColor Gray
        }
    }
    
    if (-not $serviceReady) {
        Write-Host "❌ Microservice failed to start properly" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Failed to start microservice: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ".."
    exit 1
}

# Step 6: Test email integration
Write-Host ""
Write-Host "Step 6: Testing email integration..." -ForegroundColor Yellow

$headers = @{
    'Content-Type' = 'application/json'
    'X-API-Key' = 'SPMJ-EmailService-2024-SecureKey-MOH-Malaysia'
}

$testRequest = @{
    UserId = '820228115693'
    Email = '<EMAIL>'
    UserName = 'ONG SU HANG'
    TempPassword = 'TempPass123!'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:$availablePort/api/password/reset/send" -Method POST -Body $testRequest -Headers $headers -TimeoutSec 10
    
    if ($response.success) {
        Write-Host "✅ Email integration test successful" -ForegroundColor Green
        Write-Host "   Response: $($response.message)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Email integration test failed: $($response.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Email integration test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 7: Final verification
Write-Host ""
Write-Host "=== Email Integration Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Status: ✅ FULLY INTEGRATED" -ForegroundColor Cyan
Write-Host "📍 Microservice URL: http://localhost:$availablePort" -ForegroundColor Gray
Write-Host "📧 Email Service: Active and tested" -ForegroundColor Green
Write-Host "🚫 Fallback Mode: Disabled (email-only)" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ Password recovery will now ONLY send emails" -ForegroundColor Green
Write-Host "✅ No password display on screen" -ForegroundColor Green
Write-Host "✅ Users must check their email" -ForegroundColor Green
Write-Host ""
Write-Host "Ready to test! Go to p0_Login.aspx and try password recovery." -ForegroundColor Cyan
Write-Host "User ID: 820228115693" -ForegroundColor Gray
Write-Host "Expected: Email <NAME_EMAIL>" -ForegroundColor Gray
Write-Host ""
Write-Host "🎉 Email integration is now fully operational!" -ForegroundColor Green
