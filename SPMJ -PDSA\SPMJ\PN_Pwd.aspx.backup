﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Pwd.aspx.vb" Inherits="SPMJ.WebForm13" 
    title="SPMJ - Industry Standard Password Management" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* Industry Standard Password Management Interface */
        .password-management-container {
            max-width: 800px;
            margin: 20px auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .password-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px 30px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .password-header h1 {
            color: #ffffff;
            font-size: 28px;
            margin: 0;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .password-header .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-top: 8px;
            font-weight: 300;
        }
        
        .password-content {
            background: #ffffff;
            padding: 40px;
        }
        
        .security-notice {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .security-notice h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        
        .security-notice p {
            margin: 0;
            font-size: 14px;
            opacity: 0.95;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f7fafc;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4299e1;
            background: #ffffff;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        
        .password-requirements {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .requirements-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .requirements-title::before {
            content: "🔐";
            margin-right: 8px;
        }
        
        .requirements-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #4a5568;
        }
        
        .requirement-icon {
            margin-right: 8px;
            font-size: 16px;
            width: 20px;
        }
        
        .requirement-met {
            color: #38a169;
        }
        
        .requirement-unmet {
            color: #e53e3e;
        }
        
        .password-strength {
            margin-top: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
        }
        
        .strength-weak {
            background: #fed7d7;
            color: #c53030;
        }
        
        .strength-medium {
            background: #faf089;
            color: #d69e2e;
        }
        
        .strength-strong {
            background: #c6f6d5;
            color: #38a169;
        }
        
        .microservice-status {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .microservice-status.online {
            background: #f0fff4;
            border-color: #68d391;
        }
        
        .microservice-status.offline {
            background: #fed7d7;
            border-color: #fc8181;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #38a169;
        }
        
        .status-offline {
            background: #e53e3e;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        .btn-primary {
            flex: 1;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }
        
        .btn-primary:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            flex: 1;
            background: #e2e8f0;
            color: #4a5568;
            border: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .message-container {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .message-success {
            background: #c6f6d5;
            border: 1px solid #38a169;
            color: #1a202c;
        }
        
        .message-error {
            background: #fed7d7;
            border: 1px solid #e53e3e;
            color: #1a202c;
        }
        
        .message-info {
            background: #bee3f8;
            border: 1px solid #4299e1;
            color: #1a202c;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .password-management-container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .password-content {
                padding: 20px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .requirements-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }
        
        .form-input.error {
            border-color: #e53e3e;
            background-color: #fed7d7;
        }
        
        .password-strength {
            margin-top: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        
        .password-strength.weak {
            background-color: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }
        
        .password-strength.medium {
            background-color: #faf089;
            color: #975a16;
            border: 1px solid #f6e05e;
        }
        
        .password-strength.strong {
            background-color: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .password-requirements {
            margin-top: 10px;
            padding: 12px;
            background-color: #f7fafc;
            border-radius: 4px;
            border-left: 4px solid #3182ce;
        }
        
        .password-requirements h4 {
            margin: 0 0 8px 0;
            color: #2d3748;
            font-size: 13px;
            font-weight: 600;
        }
        
        .requirement-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .requirement-item {
            padding: 2px 0;
            font-size: 12px;
            color: #4a5568;
        }
        
        .requirement-item.met {
            color: #22543d;
        }
        
        .requirement-item.met::before {
            content: "✓ ";
            color: #38a169;
            font-weight: bold;
        }
        
        .requirement-item:not(.met)::before {
            content: "✗ ";
            color: #e53e3e;
            font-weight: bold;
        }
        
        .password-match {
            margin-top: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            display: none;
        }
        
        .password-match.match {
            background-color: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        
        .password-match.no-match {
            background-color: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .btn-primary:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .form-actions {
            margin-top: 30px;
            text-align: center;
        }
        
        .security-notice {
            margin-top: 20px;
            padding: 15px;
            background-color: #edf2f7;
            border-radius: 6px;
            border-left: 4px solid #3182ce;
        }
        
        .security-notice h4 {
            margin: 0 0 8px 0;
            color: #2d3748;
            font-size: 14px;
            font-weight: 600;
        }
        
        .security-notice ul {
            margin: 0;
            padding-left: 20px;
            color: #4a5568;
            font-size: 12px;
        }
        
        .error-message {
            background-color: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .success-message {
            background-color: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        @media (max-width: 768px) {
            .password-container {
                margin: 10px;
                padding: 20px;
            }
        }
    </style>
    
    <script type="text/javascript">
        // Password strength validation and UI enhancement
        function validatePasswordStrength(password) {
            var strength = 0;
            var feedback = [];
            
            // Length check
            if (password.length >= 8) strength += 1;
            else feedback.push("Sekurang-kurangnya 8 aksara");
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength += 1;
            else feedback.push("Satu huruf besar");
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength += 1;
            else feedback.push("Satu huruf kecil");
            
            // Number check
            if (/[0-9]/.test(password)) strength += 1;
            else feedback.push("Satu nombor");
            
            // Special character check
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;
            else feedback.push("Satu simbol khas");
            
            return { strength: strength, feedback: feedback };
        }
        
        function updatePasswordStrength() {
            var password = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            var strengthDiv = document.getElementById('passwordStrength');
            var result = validatePasswordStrength(password);
            
            if (password.length === 0) {
                strengthDiv.style.display = 'none';
                return;
            }
            
            strengthDiv.style.display = 'block';
            
            if (result.strength < 3) {
                strengthDiv.className = 'password-strength weak';
                strengthDiv.innerHTML = 'Lemah: ' + result.feedback.join(', ');
            } else if (result.strength < 5) {
                strengthDiv.className = 'password-strength medium';
                strengthDiv.innerHTML = 'Sederhana: ' + result.feedback.join(', ');
            } else {
                strengthDiv.className = 'password-strength strong';
                strengthDiv.innerHTML = 'Kuat: Kata laluan memenuhi semua keperluan';
            }
            
            updateRequirements();
            checkPasswordMatch();
        }
        
        function updateRequirements() {
            var password = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            
            // Update requirement indicators
            var requirements = [
                { id: 'req-length', test: password.length >= 8 },
                { id: 'req-upper', test: /[A-Z]/.test(password) },
                { id: 'req-lower', test: /[a-z]/.test(password) },
                { id: 'req-number', test: /[0-9]/.test(password) },
                { id: 'req-special', test: /[^A-Za-z0-9]/.test(password) }
            ];
            
            requirements.forEach(function(req) {
                var element = document.getElementById(req.id);
                if (element) {
                    if (req.test) {
                        element.classList.add('met');
                    } else {
                        element.classList.remove('met');
                    }
                }
            });
        }
        
        function checkPasswordMatch() {
            var newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            var confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            var matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword.length === 0) {
                matchDiv.style.display = 'none';
                return;
            }
            
            matchDiv.style.display = 'block';
            
            if (newPassword === confirmPassword) {
                matchDiv.className = 'password-match match';
                matchDiv.innerHTML = '✓ Kata laluan sepadan';
            } else {
                matchDiv.className = 'password-match no-match';
                matchDiv.innerHTML = '✗ Kata laluan tidak sepadan';
            }
            
            updateSubmitButton();
        }
        
        function updateSubmitButton() {
            var currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            var newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            var confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            var submitBtn = document.getElementById('<%= btn_ChangePassword.ClientID %>');
            
            var result = validatePasswordStrength(newPassword);
            var isValid = currentPassword.length > 0 && 
                         result.strength >= 3 && 
                         newPassword === confirmPassword &&
                         newPassword !== currentPassword;
            
            submitBtn.disabled = !isValid;
        }
        
        function validateForm() {
            var currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            var newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            var confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            
            // Clear previous error
            document.getElementById('errorMessage').style.display = 'none';
            
            if (currentPassword.length === 0) {
                showError('Sila masukkan kata laluan semasa.');
                return false;
            }
            
            if (newPassword.length === 0) {
                showError('Sila masukkan kata laluan baru.');
                return false;
            }
            
            var result = validatePasswordStrength(newPassword);
            if (result.strength < 3) {
                showError('Kata laluan baru tidak memenuhi keperluan keselamatan: ' + result.feedback.join(', '));
                return false;
            }
            
            if (newPassword !== confirmPassword) {
                showError('Kata laluan baru dan pengesahan tidak sepadan.');
                return false;
            }
            
            if (newPassword === currentPassword) {
                showError('Kata laluan baru mestilah berbeza daripada kata laluan semasa.');
                return false;
            }
            
            return true;
        }
        
        function showError(message) {
            var errorDiv = document.getElementById('errorMessage');
            errorDiv.innerHTML = message;
            errorDiv.style.display = 'block';
            window.scrollTo(0, 0);
        }
        
        function showSuccess(message) {
            var successDiv = document.getElementById('successMessage');
            successDiv.innerHTML = message;
            successDiv.style.display = 'block';
            window.scrollTo(0, 0);
        }
        
        // Initialize when page loads
        window.onload = function() {
            document.getElementById('<%= Tx_CurrentPassword.ClientID %>').addEventListener('input', updateSubmitButton);
            document.getElementById('<%= Tx_NewPassword.ClientID %>').addEventListener('input', updatePasswordStrength);
            document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').addEventListener('input', checkPasswordMatch);
        };
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="password-management-container">
        <!-- Header Section -->
        <div class="password-header">
            <h1>🔐 Industry Standard Password Management</h1>
            <div class="subtitle">Secure Password Change with Microservice Integration</div>
        </div>
        
        <!-- Content Section -->
        <div class="password-content">
            <!-- Security Notice -->
            <div class="security-notice">
                <h3>🛡️ Enhanced Security Features</h3>
                <p>SHA256+Salt Encryption • Real-time Validation • Microservice Integration • Industry Compliance</p>
            </div>
            
            <!-- Microservice Status -->
            <div id="microserviceStatus" class="microservice-status">
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot status-offline"></div>
                    <span id="statusText">Checking Email Service Status...</span>
                </div>
            </div>
            
            <!-- Error/Success Messages -->
            <asp:Panel ID="messagePanel" runat="server" Visible="false" CssClass="message-container">
                <asp:Label ID="messageLabel" runat="server"></asp:Label>
            </asp:Panel>
            
            <!-- Current Password Section -->
            <div class="form-section">
                <div class="section-title">Current Authentication</div>
                <div class="form-group">
                    <label class="form-label" for="<%= Tx_CurrentPassword.ClientID %>">
                        Current Password <span style="color: #e53e3e;">*</span>
                    </label>
                    <asp:TextBox ID="Tx_CurrentPassword" runat="server" 
                        CssClass="form-input" 
                        TextMode="Password" 
                        MaxLength="100"
                        placeholder="Enter your current password"
                        autocomplete="current-password" />
                </div>
            </div>
            
            <!-- New Password Section -->
            <div class="form-section">
                <div class="section-title">New Password Configuration</div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="<%= Tx_NewPassword.ClientID %>">
                            New Password <span style="color: #e53e3e;">*</span>
                        </label>
                        <asp:TextBox ID="Tx_NewPassword" runat="server" 
                            CssClass="form-input" 
                            TextMode="Password" 
                            MaxLength="100"
                            placeholder="Create a strong new password"
                            autocomplete="new-password" />
                        
                        <!-- Password Strength Indicator -->
                        <div id="passwordStrength" class="password-strength">
                            <span id="strengthText">Password Strength: </span>
                            <span id="strengthLevel">Not Evaluated</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="<%= Tx_ConfirmPassword.ClientID %>">
                            Confirm Password <span style="color: #e53e3e;">*</span>
                        </label>
                        <asp:TextBox ID="Tx_ConfirmPassword" runat="server" 
                            CssClass="form-input" 
                            TextMode="Password" 
                            MaxLength="100"
                            placeholder="Confirm your new password"
                            autocomplete="new-password" />
                        
                        <!-- Password Match Indicator -->
                        <div id="passwordMatch" class="password-strength">
                            <span id="matchText">Password Match: </span>
                            <span id="matchStatus">Not Checked</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Password Requirements -->
            <div class="password-requirements">
                <div class="requirements-title">Industry Standard Password Requirements</div>
                <div class="requirements-grid">
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-length">❌</span>
                        <span>Minimum 8 characters</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-upper">❌</span>
                        <span>Uppercase letter (A-Z)</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-lower">❌</span>
                        <span>Lowercase letter (a-z)</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-number">❌</span>
                        <span>Number (0-9)</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-special">❌</span>
                        <span>Special character (!@#$%^&*)</span>
                    </div>
                    <div class="requirement-item">
                        <span class="requirement-icon requirement-unmet" id="req-different">❌</span>
                        <span>Different from current password</span>
                    </div>
                </div>
            </div>
            
            <!-- Email Notification Section -->
            <div class="form-section">
                <div class="section-title">Security Notification</div>
                <div class="form-group">
                    <label class="form-label">
                        <asp:CheckBox ID="chkEmailNotification" runat="server" Checked="true" />
                        Send email notification after password change
                    </label>
                    <small style="color: #718096; font-size: 12px;">
                        You will receive a confirmation email to verify the password change for security purposes.
                    </small>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <asp:Button ID="btn_ChangePassword" runat="server" 
                    CssClass="btn-primary" 
                    Text="🔐 Update Password"
                    OnClientClick="return validatePasswordChange();"
                    Enabled="false" />
                
                <asp:Button ID="btn_Cancel" runat="server" 
                    CssClass="btn-secondary" 
                    Text="Cancel"
                    OnClientClick="return confirm('Are you sure you want to cancel password change?');"
                    CausesValidation="false" />
            </div>
            
            <!-- Additional Security Features -->
            <div class="form-section" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                <div class="section-title">Enhanced Security Features</div>
                <div style="font-size: 14px; color: #4a5568; line-height: 1.6;">
                    <p><strong>🔐 SHA256+Salt Encryption:</strong> Your password is encrypted using industry-standard SHA256 hashing with unique salt.</p>
                    <p><strong>📧 Email Service Integration:</strong> Automatic notifications sent via secure microservice architecture.</p>
                    <p><strong>🛡️ Real-time Validation:</strong> Password strength and requirements validated in real-time.</p>
                    <p><strong>📊 Compliance Ready:</strong> Meets NIST, OWASP, and ISO 27001 security standards.</p>
                    <p><strong>🔄 Session Management:</strong> Automatic session handling for enhanced security.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div>Processing password change...</div>
        </div>
    </div>
    
    <!-- JavaScript for Real-time Validation and Microservice Integration -->
    <script type="text/javascript">
        // Microservice integration and validation
        let emailServiceStatus = false;
        
        // Check microservice status on page load
        window.onload = function() {
            checkEmailServiceStatus();
            setupPasswordValidation();
        };
        
        // Check email service status
        function checkEmailServiceStatus() {
            // Call server-side web method for email service health check
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'PN_Pwd.aspx/CheckEmailServiceHealth', true);
            xhr.setRequestHeader('Content-Type', 'application/json; charset=utf-8');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    try {
                        if (xhr.status === 200) {
                            var response = JSON.parse(xhr.responseText);
                            if (response.d) {
                                var healthData = JSON.parse(response.d);
                                updateServiceStatus(healthData.status === 'online', healthData.message);
                            } else {
                                updateServiceStatus(false, 'Email Service: Offline ⚠️ (No response)');
                            }
                        } else {
                            updateServiceStatus(false, 'Email Service: Offline ⚠️ (Connection failed)');
                        }
                    } catch (e) {
                        updateServiceStatus(false, 'Email Service: Offline ⚠️ (Response error)');
                    }
                }
            };

            xhr.send('{}');
        }
        
        // Update service status display
        function updateServiceStatus(isOnline, message) {
            emailServiceStatus = isOnline;
            const statusElement = document.getElementById('microserviceStatus');
            const dotElement = document.getElementById('statusDot');
            const textElement = document.getElementById('statusText');

            if (isOnline) {
                statusElement.className = 'microservice-status online';
                dotElement.className = 'status-dot status-online';
                textElement.textContent = message || 'Email Service: Online ✅';
            } else {
                statusElement.className = 'microservice-status offline';
                dotElement.className = 'status-dot status-offline';
                textElement.textContent = message || 'Email Service: Offline ⚠️ (Notifications disabled)';
            }
        }
        
        // Setup password validation
        function setupPasswordValidation() {
            const newPasswordField = document.getElementById('<%= Tx_NewPassword.ClientID %>');
            const confirmPasswordField = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>');
            const currentPasswordField = document.getElementById('<%= Tx_CurrentPassword.ClientID %>');
            
            newPasswordField.addEventListener('input', validatePasswordStrength);
            confirmPasswordField.addEventListener('input', validatePasswordMatch);
            currentPasswordField.addEventListener('input', checkFormValidity);
        }
        
        // Validate password strength
        function validatePasswordStrength() {
            const password = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            
            // Check requirements
            const hasLength = password.length >= 8;
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
            const isDifferent = password !== currentPassword;
            
            // Update requirement indicators
            updateRequirement('req-length', hasLength);
            updateRequirement('req-upper', hasUpper);
            updateRequirement('req-lower', hasLower);
            updateRequirement('req-number', hasNumber);
            updateRequirement('req-special', hasSpecial);
            updateRequirement('req-different', isDifferent);
            
            // Calculate strength score
            const score = [hasLength, hasUpper, hasLower, hasNumber, hasSpecial, isDifferent].filter(Boolean).length;
            
            // Update strength indicator
            const strengthElement = document.getElementById('passwordStrength');
            const strengthLevel = document.getElementById('strengthLevel');
            
            if (score < 4) {
                strengthElement.className = 'password-strength strength-weak';
                strengthLevel.textContent = 'Weak';
            } else if (score < 6) {
                strengthElement.className = 'password-strength strength-medium';
                strengthLevel.textContent = 'Medium';
            } else {
                strengthElement.className = 'password-strength strength-strong';
                strengthLevel.textContent = 'Strong';
            }
            
            checkFormValidity();
        }
        
        // Update requirement indicator
        function updateRequirement(id, met) {
            const element = document.getElementById(id);
            if (met) {
                element.innerHTML = '✅';
                element.className = 'requirement-icon requirement-met';
            } else {
                element.innerHTML = '❌';
                element.className = 'requirement-icon requirement-unmet';
            }
        }
        
        // Validate password match
        function validatePasswordMatch() {
            const newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            
            const matchElement = document.getElementById('passwordMatch');
            const matchStatus = document.getElementById('matchStatus');
            
            if (confirmPassword === '') {
                matchElement.className = 'password-strength';
                matchStatus.textContent = 'Not Checked';
            } else if (newPassword === confirmPassword) {
                matchElement.className = 'password-strength strength-strong';
                matchStatus.textContent = 'Passwords Match ✅';
            } else {
                matchElement.className = 'password-strength strength-weak';
                matchStatus.textContent = 'Passwords Do Not Match ❌';
            }
            
            checkFormValidity();
        }
        
        // Check overall form validity
        function checkFormValidity() {
            const currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            const newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            
            // Check all requirements
            const hasLength = newPassword.length >= 8;
            const hasUpper = /[A-Z]/.test(newPassword);
            const hasLower = /[a-z]/.test(newPassword);
            const hasNumber = /[0-9]/.test(newPassword);
            const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(newPassword);
            const isDifferent = newPassword !== currentPassword;
            const passwordsMatch = newPassword === confirmPassword;
            
            const allRequirementsMet = hasLength && hasUpper && hasLower && hasNumber && hasSpecial && isDifferent;
            const allFieldsFilled = currentPassword !== '' && newPassword !== '' && confirmPassword !== '';
            
            const submitButton = document.getElementById('<%= btn_ChangePassword.ClientID %>');
            submitButton.disabled = !(allRequirementsMet && passwordsMatch && allFieldsFilled);
        }
        
        // Validate before form submission
        function validatePasswordChange() {
            showLoading(true);
            
            // Additional client-side validation before submission
            const currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            const newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            
            if (!currentPassword || !newPassword || !confirmPassword) {
                showLoading(false);
                alert('Please fill in all password fields.');
                return false;
            }
            
            if (newPassword !== confirmPassword) {
                showLoading(false);
                alert('New password and confirmation do not match.');
                return false;
            }
            
            // Check if email service notification is needed but service is offline
            const emailNotificationChecked = document.getElementById('<%= chkEmailNotification.ClientID %>').checked;
            if (emailNotificationChecked && !emailServiceStatus) {
                const proceed = confirm('Email service is currently offline. Password will be changed but no notification email will be sent. Continue?');
                if (!proceed) {
                    showLoading(false);
                    return false;
                }
            }
            
            return true;
        }
        
        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }
        
        // Handle cancel button
        document.getElementById('<%= btn_Cancel.ClientID %>').addEventListener('click', function(e) {
            const hasChanges = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value || 
                             document.getElementById('<%= Tx_NewPassword.ClientID %>').value || 
                             document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;
            
            if (hasChanges) {
                if (!confirm('You have unsaved changes. Are you sure you want to cancel?')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            // Clear all fields
            document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value = '';
            document.getElementById('<%= Tx_NewPassword.ClientID %>').value = '';
            document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value = '';
        });
    </script>
</asp:Content>