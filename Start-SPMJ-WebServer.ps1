# Start SPMJ Web Application for Testing
Write-Host "=== Starting SPMJ Web Application ===" -ForegroundColor Green

$webAppPath = "SPMJ -PDSA\SPMJ"
$port = 8080

# Check if the web application directory exists
if (-not (Test-Path $webAppPath)) {
    Write-Host "❌ Web application directory not found: $webAppPath" -ForegroundColor Red
    exit 1
}

Write-Host "Web Application Path: $webAppPath" -ForegroundColor Gray
Write-Host "Port: $port" -ForegroundColor Gray

# Try to use IIS Express if available
$iisExpressPath = "${env:ProgramFiles}\IIS Express\iisexpress.exe"
if (-not (Test-Path $iisExpressPath)) {
    $iisExpressPath = "${env:ProgramFiles(x86)}\IIS Express\iisexpress.exe"
}

if (Test-Path $iisExpressPath) {
    Write-Host "Found IIS Express: $iisExpressPath" -ForegroundColor Green
    Write-Host "Starting IIS Express..." -ForegroundColor Yellow
    
    $fullPath = Resolve-Path $webAppPath
    
    try {
        # Start IIS Express
        $process = Start-Process -FilePath $iisExpressPath -ArgumentList "/path:$fullPath", "/port:$port" -PassThru -WindowStyle Normal
        
        Write-Host "✅ IIS Express started with PID: $($process.Id)" -ForegroundColor Green
        Write-Host "Web application should be available at: http://localhost:$port" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Test URLs:" -ForegroundColor Yellow
        Write-Host "  Login Page: http://localhost:$port/p0_Login.aspx" -ForegroundColor Gray
        Write-Host "  OTP Test Page: http://localhost:$port/TestOtpIntegration.aspx" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
        
        # Wait for the process to exit or user to press Ctrl+C
        try {
            $process.WaitForExit()
        } catch {
            Write-Host "Server stopped" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "❌ Failed to start IIS Express: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ IIS Express not found" -ForegroundColor Red
    Write-Host "Trying alternative method..." -ForegroundColor Yellow
    
    # Try using Python HTTP server as fallback
    try {
        $pythonPath = Get-Command python -ErrorAction SilentlyContinue
        if ($pythonPath) {
            Write-Host "Using Python HTTP server as fallback..." -ForegroundColor Yellow
            Set-Location $webAppPath
            python -m http.server $port
        } else {
            Write-Host "❌ Python not found either" -ForegroundColor Red
            Write-Host ""
            Write-Host "To test the web application, you can:" -ForegroundColor Yellow
            Write-Host "1. Install IIS Express" -ForegroundColor Gray
            Write-Host "2. Use Visual Studio to run the project" -ForegroundColor Gray
            Write-Host "3. Deploy to IIS" -ForegroundColor Gray
            Write-Host ""
            Write-Host "For now, you can test the microservice integration using:" -ForegroundColor Yellow
            Write-Host "  .\Test-Real-User-OTP.ps1" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ Failed to start alternative server: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Server Startup Complete ===" -ForegroundColor Green
