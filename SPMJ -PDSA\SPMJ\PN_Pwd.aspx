<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Pwd.aspx.vb" Inherits="SPMJ.WebForm13" 
    title="SPMJ - Password Management" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        /* Modern Password Management Interface */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --border-radius: 12px;
            --box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .password-management-container {
            max-width: 600px;
            margin: 30px auto;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .password-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .password-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .password-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .password-header .subtitle {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 1rem;
            position: relative;
            z-index: 1;
        }

        .password-content {
            padding: 40px;
        }

        .microservice-status {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 14px;
            transition: var(--transition);
        }

        .microservice-status.online {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #b8dabc;
        }

        .microservice-status.offline {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border: 1px solid #f0d43a;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 12px;
            animation: pulse 2s infinite;
        }

        .status-online {
            background: var(--success-color);
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }

        .status-offline {
            background: var(--warning-color);
            box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        .alert {
            border: none;
            border-radius: 8px;
            padding: 18px 20px;
            margin-bottom: 25px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .alert-success::before {
            background: var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            color: #721c24;
        }

        .alert-danger::before {
            background: var(--danger-color);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .alert-warning::before {
            background: var(--warning-color);
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .form-label .required {
            color: var(--danger-color);
            margin-left: 4px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px 20px;
            font-size: 15px;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.9);
            width: 100%;
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.15);
            background: white;
            outline: none;
        }

        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 8px;
            font-size: 18px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .password-toggle:hover {
            color: var(--secondary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .btn {
            border-radius: 8px;
            padding: 15px 35px;
            font-weight: 600;
            font-size: 15px;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
        }

        .password-requirements {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .requirements-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            transition: var(--transition);
        }

        .requirement i {
            margin-right: 10px;
            width: 18px;
            font-size: 12px;
        }

        .requirement.valid {
            color: var(--success-color);
        }

        .requirement.invalid {
            color: var(--danger-color);
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--secondary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .password-management-container {
                margin: 15px;
                max-width: none;
            }
            
            .password-content {
                padding: 25px 20px;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                padding: 12px 25px;
                font-size: 14px;
            }
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="password-management-container">
        <!-- Header -->
        <div class="password-header">
            <h1><i class="fas fa-shield-alt"></i> Password Management</h1>
            <div class="subtitle">Secure Password Change System</div>
        </div>

        <!-- Content -->
        <div class="password-content">
            <!-- Email Service Status -->
            <div id="microserviceStatus" class="microservice-status offline">
                <div id="statusDot" class="status-dot status-offline"></div>
                <span id="statusText">Email Service: Checking...</span>
            </div>

            <!-- Messages -->
            <asp:Panel ID="pnl_Messages" runat="server" Visible="false">
                <asp:Literal ID="lit_Messages" runat="server"></asp:Literal>
            </asp:Panel>

            <!-- Current Authentication Section -->
            <div class="form-section">
                <div class="section-title">Current Authentication</div>

                <div class="form-group">
                    <label class="form-label" for="<%= Tx_CurrentPassword.ClientID %>">
                        Current Password <span class="required">*</span>
                    </label>
                    <div class="password-input-group">
                        <asp:TextBox ID="Tx_CurrentPassword" runat="server"
                                   TextMode="Password"
                                   CssClass="form-control"
                                   placeholder="Enter your current password"
                                   autocomplete="current-password"></asp:TextBox>
                        <button type="button" class="password-toggle" onclick="togglePassword('Tx_CurrentPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- New Password Section -->
            <div class="form-section">
                <div class="section-title">New Password</div>

                <div class="form-group">
                    <label class="form-label" for="<%= Tx_NewPassword.ClientID %>">
                        New Password <span class="required">*</span>
                    </label>
                    <div class="password-input-group">
                        <asp:TextBox ID="Tx_NewPassword" runat="server"
                                   TextMode="Password"
                                   CssClass="form-control"
                                   placeholder="Enter your new password"
                                   autocomplete="new-password"></asp:TextBox>
                        <button type="button" class="password-toggle" onclick="togglePassword('Tx_NewPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="<%= Tx_ConfirmPassword.ClientID %>">
                        Confirm New Password <span class="required">*</span>
                    </label>
                    <div class="password-input-group">
                        <asp:TextBox ID="Tx_ConfirmPassword" runat="server"
                                   TextMode="Password"
                                   CssClass="form-control"
                                   placeholder="Confirm your new password"
                                   autocomplete="new-password"></asp:TextBox>
                        <button type="button" class="password-toggle" onclick="togglePassword('Tx_ConfirmPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Password Requirements -->
                <div class="password-requirements">
                    <div class="requirements-title">Password Requirements</div>
                    <div id="passwordRequirements">
                        <div class="requirement invalid" id="req-length">
                            <i class="fas fa-times"></i>
                            At least 8 characters long
                        </div>
                        <div class="requirement invalid" id="req-uppercase">
                            <i class="fas fa-times"></i>
                            Contains uppercase letter
                        </div>
                        <div class="requirement invalid" id="req-lowercase">
                            <i class="fas fa-times"></i>
                            Contains lowercase letter
                        </div>
                        <div class="requirement invalid" id="req-number">
                            <i class="fas fa-times"></i>
                            Contains number
                        </div>
                        <div class="requirement invalid" id="req-special">
                            <i class="fas fa-times"></i>
                            Contains special character
                        </div>
                        <div class="requirement invalid" id="req-match">
                            <i class="fas fa-times"></i>
                            Passwords match
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="button-group">
                <asp:Button ID="btn_ChangePassword" runat="server"
                          Text="Change Password"
                          CssClass="btn btn-primary"
                          OnClientClick="return validateForm();" />
                <asp:Button ID="btn_Cancel" runat="server"
                          Text="Cancel"
                          CssClass="btn btn-secondary"
                          CausesValidation="false" />
            </div>

            <!-- Loading Overlay -->
            <div id="loadingOverlay" class="loading">
                <div class="spinner"></div>
                <div>Processing password change...</div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        // Global variables
        let emailServiceStatus = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkEmailServiceStatus();
            setupPasswordValidation();
            setupFormHandlers();
        });

        // Email service health check
        function checkEmailServiceStatus() {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'PN_Pwd.aspx/CheckEmailServiceHealth', true);
            xhr.setRequestHeader('Content-Type', 'application/json; charset=utf-8');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    try {
                        if (xhr.status === 200) {
                            var response = JSON.parse(xhr.responseText);
                            if (response.d) {
                                var healthData = JSON.parse(response.d);
                                updateServiceStatus(healthData.status === 'online', healthData.message);
                            } else {
                                updateServiceStatus(false, 'Email Service: Offline ⚠️ (No response)');
                            }
                        } else {
                            updateServiceStatus(false, 'Email Service: Offline ⚠️ (Connection failed)');
                        }
                    } catch (e) {
                        updateServiceStatus(false, 'Email Service: Offline ⚠️ (Response error)');
                    }
                }
            };

            xhr.send('{}');
        }

        // Update service status display
        function updateServiceStatus(isOnline, message) {
            emailServiceStatus = isOnline;
            const statusElement = document.getElementById('microserviceStatus');
            const dotElement = document.getElementById('statusDot');
            const textElement = document.getElementById('statusText');

            if (isOnline) {
                statusElement.className = 'microservice-status online';
                dotElement.className = 'status-dot status-online';
                textElement.textContent = message || 'Email Service: Online ✅';
            } else {
                statusElement.className = 'microservice-status offline';
                dotElement.className = 'status-dot status-offline';
                textElement.textContent = message || 'Email Service: Offline ⚠️ (Notifications disabled)';
            }
        }

        // Password visibility toggle
        function togglePassword(fieldId) {
            const field = document.getElementById('<%= Tx_CurrentPassword.ClientID %>'.replace('Tx_CurrentPassword', fieldId));
            const button = field.nextElementSibling;
            const icon = button.querySelector('i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // Setup password validation
        function setupPasswordValidation() {
            const newPasswordField = document.getElementById('<%= Tx_NewPassword.ClientID %>');
            const confirmPasswordField = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>');

            newPasswordField.addEventListener('input', validatePassword);
            confirmPasswordField.addEventListener('input', validatePasswordMatch);
        }

        // Validate password requirements
        function validatePassword() {
            const password = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;

            // Length requirement
            updateRequirement('req-length', password.length >= 8);

            // Uppercase requirement
            updateRequirement('req-uppercase', /[A-Z]/.test(password));

            // Lowercase requirement
            updateRequirement('req-lowercase', /[a-z]/.test(password));

            // Number requirement
            updateRequirement('req-number', /\d/.test(password));

            // Special character requirement
            updateRequirement('req-special', /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password));

            // Also validate password match when new password changes
            validatePasswordMatch();
        }

        // Validate password match
        function validatePasswordMatch() {
            const newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;

            const isMatch = newPassword === confirmPassword && newPassword.length > 0;
            updateRequirement('req-match', isMatch);
        }

        // Update requirement display
        function updateRequirement(reqId, isValid) {
            const element = document.getElementById(reqId);
            const icon = element.querySelector('i');

            if (isValid) {
                element.className = 'requirement valid';
                icon.className = 'fas fa-check';
            } else {
                element.className = 'requirement invalid';
                icon.className = 'fas fa-times';
            }
        }

        // Form validation
        function validateForm() {
            const currentPassword = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value;
            const newPassword = document.getElementById('<%= Tx_NewPassword.ClientID %>').value;
            const confirmPassword = document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;

            // Check if all fields are filled
            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('Please fill in all password fields.');
                return false;
            }

            // Check password requirements
            const requirements = [
                newPassword.length >= 8,
                /[A-Z]/.test(newPassword),
                /[a-z]/.test(newPassword),
                /\d/.test(newPassword),
                /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(newPassword),
                newPassword === confirmPassword
            ];

            if (!requirements.every(req => req)) {
                alert('Please ensure your new password meets all requirements.');
                return false;
            }

            // Check if new password is different from current
            if (currentPassword === newPassword) {
                alert('New password must be different from current password.');
                return false;
            }

            // Show loading
            showLoading(true);
            return true;
        }

        // Setup form handlers
        function setupFormHandlers() {
            // Handle form submission
            const form = document.forms[0];
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }
            });

            // Handle cancel button
            document.getElementById('<%= btn_Cancel.ClientID %>').addEventListener('click', function(e) {
                const hasChanges = document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value ||
                                 document.getElementById('<%= Tx_NewPassword.ClientID %>').value ||
                                 document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value;

                if (hasChanges) {
                    if (!confirm('You have unsaved changes. Are you sure you want to cancel?')) {
                        e.preventDefault();
                        return false;
                    }
                }

                // Clear all fields
                document.getElementById('<%= Tx_CurrentPassword.ClientID %>').value = '';
                document.getElementById('<%= Tx_NewPassword.ClientID %>').value = '';
                document.getElementById('<%= Tx_ConfirmPassword.ClientID %>').value = '';
            });
        }

        // Show/hide loading overlay
        function showLoading(show) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'block' : 'none';
        }

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messagePanel = document.getElementById('<%= pnl_Messages.ClientID %>');
            if (messagePanel && messagePanel.style.display !== 'none') {
                messagePanel.style.opacity = '0';
                setTimeout(function() {
                    messagePanel.style.display = 'none';
                }, 300);
            }
        }, 5000);
    </script>
</asp:Content>
