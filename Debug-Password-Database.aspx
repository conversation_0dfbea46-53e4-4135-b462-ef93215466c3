<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="Debug-Password-Database.aspx.vb" Inherits="SPMJ.DebugPasswordDatabase" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Debug Password Database</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .warning { background: #fff3e0; color: #ef6c00; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>🔍 Password Database Debug Tool</h1>
        
        <div class="debug-info">
            <h3>User ID to Check:</h3>
            <asp:TextBox ID="txt_UserId" runat="server" placeholder="Enter User ID"></asp:TextBox>
            <asp:Button ID="btn_Check" runat="server" Text="Check Password Data" />
        </div>
        
        <asp:Panel ID="pnl_Results" runat="server" Visible="false">
            <h3>Database Query Results:</h3>
            <asp:Literal ID="lit_Results" runat="server"></asp:Literal>
        </asp:Panel>
        
        <div class="debug-info">
            <h3>Test Password Verification:</h3>
            <asp:TextBox ID="txt_TestPassword" runat="server" placeholder="Enter password to test" TextMode="Password"></asp:TextBox>
            <asp:Button ID="btn_TestPassword" runat="server" Text="Test Password" />
            <br />
            <asp:Literal ID="lit_TestResults" runat="server"></asp:Literal>
        </div>
    </form>
</body>
</html>
